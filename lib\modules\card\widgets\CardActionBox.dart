import 'package:flutter/material.dart';

class CardActionBox extends StatelessWidget {
  final String iconAssetPath;
  final String label;
  final VoidCallback onTap;

  const CardActionBox({
    super.key,
    required this.iconAssetPath,
    required this.label,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double baseWidth = 390; // base design width
    final double scale = screenWidth / baseWidth;

     // Scaled values
    final double iconWidth = 65 * scale;
    final double iconHeight = 60 * scale;
    final double fontSize = 14 * scale.clamp(0.85, 1.1); // limit scaling
    final double verticalPadding = 10 * scale.clamp(0.8, 1.2);
    final double horizontalPadding = 20 * scale.clamp(0.8, 1.2);

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
         padding: EdgeInsets.symmetric(
          vertical: verticalPadding,
          horizontal: horizontalPadding,
          ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: const Color(0xFFFFBF00), // stroke color
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 4,
              offset: Offset(0, 6),
            ),
            BoxShadow(
              color: Colors.white10,
              blurRadius: 2,
              offset: Offset(0,-1),
            )
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              iconAssetPath,
              width: iconWidth,
              height: iconHeight,
              fit: BoxFit.contain,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: const Color(0xFF474747),
                fontSize: fontSize,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}