import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../../view/PurchaseCardPage.dart';

class PurchaseCardLoader extends StatefulWidget {
  const PurchaseCardLoader({super.key});

  @override
  State<PurchaseCardLoader> createState() => _PurchaseCardLoaderState();
}

class _PurchaseCardLoaderState extends State<PurchaseCardLoader> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const PurchaseCardPage()),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    const sliderHeight = 334.0;
    final double sliderHeightScaled = sliderHeight * (screenWidth / 390);
    final double sideMargin = screenWidth * 0.06; // ~24 on 390px width

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 70 * (screenWidth / 390),
        leading: Shimmer.fromColors(
          baseColor: Colors.grey.shade300,
          highlightColor: Colors.grey.shade100,
          child: Container(
            width: 40 * (screenWidth / 390),
            height: 40 * (screenWidth / 390),
            margin: EdgeInsets.all(8 * (screenWidth / 390)),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        title: Shimmer.fromColors(
          baseColor: Colors.grey.shade300,
          highlightColor: Colors.grey.shade100,
          child: Container(
            width: 180 * (screenWidth / 390),
            height: 20 * (screenWidth / 390),
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.white,
        centerTitle: true,
        actions: [
          Shimmer.fromColors(
            baseColor: Colors.grey.shade300,
            highlightColor: Colors.grey.shade100,
            child: Container(
              width: 40 * (screenWidth / 390),
              height: 40 * (screenWidth / 390),
              margin: EdgeInsets.all(8 * (screenWidth / 390)),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: sideMargin),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Image slider shimmer
              SizedBox(height: 16 * (screenWidth / 390)),
              Shimmer.fromColors(
                baseColor: Colors.grey.shade300,
                highlightColor: Colors.grey.shade100,
                child: Container(
                  height: sliderHeightScaled,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16 * (screenWidth / 390)),
                  ),
                ),
              ),
              SizedBox(height: 16 * (screenWidth / 390)),
              // Dots shimmer
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(4, (index) => Padding(
                  padding: EdgeInsets.symmetric(horizontal: 4 * (screenWidth / 390)),
                  child: Shimmer.fromColors(
                    baseColor: Colors.grey.shade300,
                    highlightColor: Colors.grey.shade100,
                    child: Container(
                      width: 10 * (screenWidth / 390),
                      height: 10 * (screenWidth / 390),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                )),
              ),
              SizedBox(height: 16 * (screenWidth / 390)),
              // CategoryDescriptionBox shimmer
              Shimmer.fromColors(
                baseColor: Colors.grey.shade300,
                highlightColor: Colors.grey.shade100,
                child: Container(
                  height: 60 * (screenWidth / 390),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12 * (screenWidth / 390)),
                  ),
                ),
              ),
              SizedBox(height: 16 * (screenWidth / 390)),
              // PriceAndQuantitySection shimmer
              Shimmer.fromColors(
                baseColor: Colors.grey.shade300,
                highlightColor: Colors.grey.shade100,
                child: Container(
                  height: 60 * (screenWidth / 390),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12 * (screenWidth / 390)),
                  ),
                ),
              ),
              SizedBox(height: 16 * (screenWidth / 390)),
              // CategoryButtonsRow shimmer
              Row(
                children: [
                  Expanded(
                    child: Shimmer.fromColors(
                      baseColor: Colors.grey.shade300,
                      highlightColor: Colors.grey.shade100,
                      child: Container(
                        height: 44 * (screenWidth / 390),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10 * (screenWidth / 390)),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 12 * (screenWidth / 390)),
                  Expanded(
                    child: Shimmer.fromColors(
                      baseColor: Colors.grey.shade300,
                      highlightColor: Colors.grey.shade100,
                      child: Container(
                        height: 44 * (screenWidth / 390),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10 * (screenWidth / 390)),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 24 * (screenWidth / 390)),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.only(
          left: sideMargin,
          right: sideMargin,
          bottom: 16 * (screenWidth / 390),
          top: 8 * (screenWidth / 390),
        ),
        child: Shimmer.fromColors(
          baseColor: Colors.grey.shade300,
          highlightColor: Colors.grey.shade100,
          child: Container(
            height: 60 * (screenWidth / 390),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16 * (screenWidth / 390)),
            ),
          ),
        ),
      ),
    );
  }
}