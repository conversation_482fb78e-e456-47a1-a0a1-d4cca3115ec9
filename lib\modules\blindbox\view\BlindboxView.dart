import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import '../widgets/BlindboxBanner.dart';
import '../widgets/PostCard.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'PostView.dart';
import '../view-model/BlindboxVM.dart';
import 'package:flutter_svg/flutter_svg.dart';

class BlindboxView extends StatelessWidget {
  const BlindboxView({super.key});

  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  int getResponsiveCrossAxisCount(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 2;
    } else if (screenWidth < 900) {
      return 3;
    } else if (screenWidth < 1200) {
      return 4;
    } else {
      return 5;
    }
  }

  double getResponsiveHeaderHeight(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 150;
    } else if (screenWidth < 900) {
      return 180;
    } else {
      return 200;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => BlindboxVM(),
      child: Consumer<BlindboxVM>(
        builder: (context, vm, _) {
          final screenWidth = MediaQuery.of(context).size.width;
          final crossAxisCount = getResponsiveCrossAxisCount(context);
          final headerHeight = getResponsiveHeaderHeight(context);
          return Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              backgroundColor: Color(0xFFFCD255),
              clipBehavior: Clip.hardEdge,
              elevation: 0,
              toolbarHeight: 0,
            ),
            body: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Gradient header (responsive height)
                  Container(
                    width: double.infinity,
                    height: headerHeight,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color(0xFFFCD255),
                          Color(0xFFFCD255),
                          Color(0xFFFFEAAB),
                          Color(0xFFFFEAAB),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        stops: [0.0, 0.46, 0.67, 1.0],
                      ),
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(50),
                        bottomRight: Radius.circular(50),
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "Unbox The Magic!",
                          style: TextStyle(
                            fontSize: getResponsiveFontSize(40, context),
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF1F185A),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Overlap the banner, section titles, and grid with the gradient
                  Transform.translate(
                    offset: const Offset(0, -50),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: screenWidth < 600 ? 15 : 20,
                          ),
                          child: BlindBoxBanner(),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                            left: screenWidth < 600 ? 16 : 20,
                            right: screenWidth < 600 ? 16 : 20,
                            top: 10,
                            bottom: 0,
                          ),
                          child: Text(
                            "Recent Unboxings by the Community",
                            style: TextStyle(
                              fontSize: getResponsiveFontSize(18, context),
                              fontWeight: FontWeight.w700,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                            left: screenWidth < 600 ? 16 : 20,
                            right: screenWidth < 600 ? 16 : 20,
                            top: 2,
                            bottom: 0,
                          ),
                          child: Text(
                            "See what others have pulled!",
                            style: TextStyle(
                              fontSize: getResponsiveFontSize(14, context),
                              fontWeight: FontWeight.normal,
                              color: Colors.black54,
                            ),
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.only(
                            top: 10,
                            left: screenWidth < 600 ? 12 : 16,
                            right: screenWidth < 600 ? 12 : 16,
                            bottom: 0,
                          ),
                          child: vm.posts.isEmpty
                              ? Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        height: getResponsiveFontSize(
                                          40,
                                          context,
                                        ),
                                      ),
                                      SvgPicture.asset(
                                        'assets/icons/Empty Blind Box.svg',
                                        width: getResponsiveFontSize(
                                          160,
                                          context,
                                        ),
                                        height: getResponsiveFontSize(
                                          160,
                                          context,
                                        ),
                                        colorFilter: ColorFilter.mode(
                                          Colors.grey,
                                          BlendMode.srcIn,
                                        ),
                                      ),
                                      SizedBox(
                                        height: getResponsiveFontSize(
                                          32,
                                          context,
                                        ),
                                      ),
                                      Text(
                                        'Looks like no one has spilled the surprise...\nBe the first to reveal what you got!\nLet the fun begin!',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontSize: getResponsiveFontSize(
                                            18,
                                            context,
                                          ),
                                          color: Colors.grey,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : MasonryGridView.count(
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  crossAxisCount: crossAxisCount,
                                  mainAxisSpacing: screenWidth < 600 ? 8 : 12,
                                  crossAxisSpacing: screenWidth < 600 ? 8 : 12,
                                  itemCount: vm.posts.length,
                                  itemBuilder: (context, index) {
                                    if (vm.isLoading) {
                                      return const PostCardShimmer();
                                    }
                                    final post = vm.posts[index];
                                    return PostCard(
                                      imageUrl: post['imageUrl'] as String,
                                      title: post['title'] as String,
                                      username: post['username'] as String,
                                      likeCount: post['likeCount'] as int,
                                      isFavorite: post['isFavorite'] as bool,
                                      onTap: () {
                                        context.push('/blind-box/post/${post['id'] ?? 'unknown'}', extra: post);
                                      },
                                    );
                                  },
                                ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
