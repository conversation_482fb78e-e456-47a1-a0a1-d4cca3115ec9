import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../view/CardPage.dart'; // Assuming CardPage exists

class PaymentResultPhysicalPage extends StatelessWidget {
  final bool isSuccess;
  final String message;

  const PaymentResultPhysicalPage({
    super.key,
    required this.isSuccess,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final contentWidth = screenWidth < 360 ? screenWidth * 0.9 : 332.0;

    final svgIconPath = isSuccess
        ? 'assets/icons/checkOutCard/OrderSuccessful.svg'
        : 'assets/icons/checkOutCard/PaymentFailed.svg';

    final title = isSuccess ? "Payment Successful!" : "Payment Failed";
    final primaryButtonLabel = isSuccess ? "View Product" : "Pay Again";
    final secondaryButtonLabel = "Back to Main Page";

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  svgIconPath,
                  width: screenWidth * 0.35,
                  height: screenWidth * 0.35,
                  colorFilter: ColorFilter.mode(
                    isSuccess ? Colors.amber : Colors.redAccent,
                    BlendMode.srcIn,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 12),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Text(
                    message,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black54,
                    ),
                  ),
                ),
                const SizedBox(height: 36),

                // Primary Button
                SizedBox(
                  width: contentWidth,
                  child: ElevatedButton(
                    onPressed: () {
                      // No navigation needed for now (structure only)
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      backgroundColor: isSuccess ? Colors.amber : Colors.redAccent,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 3,
                    ),
                    child: Text(
                      primaryButtonLabel,
                      style: const TextStyle(
                        fontSize: 15.5,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 12),

                // Secondary Button
                SizedBox(
                  width: contentWidth,
                  child: OutlinedButton(
                    onPressed: () {
                      Navigator.pushAndRemoveUntil(
                        context,
                        MaterialPageRoute(builder: (_) => const CardPage()),
                        (route) => false,
                      );
                    },
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      side: BorderSide(
                        color: isSuccess ? Colors.amber : Colors.redAccent,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      secondaryButtonLabel,
                      style: TextStyle(
                        fontSize: 15.5,
                        fontWeight: FontWeight.bold,
                        color: isSuccess ? Colors.amber : Colors.redAccent,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
