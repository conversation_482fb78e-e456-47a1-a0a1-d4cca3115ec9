import 'package:flutter/material.dart';
import '../loadings/CardPageLoader.dart';
import '../loadings/PurchaseCard/CheckOutLoader.dart';
import 'package:flutter_svg/svg.dart';
// import 'RevealCardPage.dart'; // Stub if needed

class PurchaseResultPage extends StatelessWidget {
  final Map<String, dynamic> resultData;

  const PurchaseResultPage({super.key, required this.resultData});

  bool get isSuccess => resultData['status'] == 'success';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Replace with your own image or SVG with color
              SvgPicture.asset(
                isSuccess
                    ? 'assets/icons/checkOutCard/OrderSuccessful.svg'
                    : 'assets/icons/checkOutCard/PaymentFailed.svg',
                height: 200,
                colorFilter: ColorFilter.mode(
                  isSuccess ? Colors.green : Colors.red,
                  BlendMode.srcIn,
                ),
              ),

              const SizedBox(height: 32),
              ShaderMask(
                shaderCallback: (context) {
                  return LinearGradient(
                    colors: isSuccess
                        ? [Colors.green, Colors.lightGreen]
                        : [Colors.red, Colors.deepOrange],
                  ).createShader(
                    Rect.fromLTWH(0, 0, context.size.width, 100),
                  );
                },
                child :Text(
                  isSuccess ? 'Purchase Card Success!' : 'Purchase Failed',
                  
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white, 
                  ),
                ),
                
              ),
              

              if (!isSuccess) ...[
                const SizedBox(height: 12),
                const Text(
                  'Please try again\nor use a different payment method',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],

              const SizedBox(height: 32),

              //Buttons with borderRadius: 6
              Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: isSuccess ? Colors.amber : Colors.red,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    onPressed: () {
                      if (isSuccess) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text("Reveal card coming soon...")),
                        );
                      } else {
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                            builder: (_) => const CheckOutLoader(
                              quantity: 5,
                              total: 5.00,
                            ),
                          ),
                        );
                      }
                    },
                    child: Text(
                      isSuccess ? 'Reveal Card' : 'Pay Again',
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  const SizedBox(height: 16),
                  OutlinedButton(
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      side: const BorderSide(color: Colors.black26),
                    ),
                    onPressed: () {
                      Navigator.pushAndRemoveUntil(
                        context,
                        MaterialPageRoute(builder: (_) => const CardPageLoader()),
                        (route) => false,
                      );
                    },
                    child: const Text(
                      'Back to Main Page',
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
