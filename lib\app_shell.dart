import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import 'modules/nav/tabItem.dart';

class AppShell extends StatefulWidget {
  final Widget child;
  
  const AppShell({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<AppShell> createState() => _AppShellState();
}

class _AppShellState extends State<AppShell> {
  TabItem? _justTapped;
  
  final Map<TabItem, bool> _hoverStates = {
    for (var tab in TabItem.values) tab: false,
  };

  final Map<TabItem, bool> _isAnimating = {
    for (var tab in TabItem.values) tab: false,
  };

  TabItem _getCurrentTab(BuildContext context) {
    final location = GoRouterState.of(context).uri.path;
    
    if (location.startsWith('/shop')) return TabItem.shop;
    if (location.startsWith('/card')) return TabItem.card;
    if (location.startsWith('/lucky-draw')) return TabItem.luckyDraw;
    if (location.startsWith('/blind-box')) return TabItem.blindBox;
    if (location.startsWith('/profile')) return TabItem.me;
    
    return TabItem.shop; // default
  }

  void _onTabSelected(TabItem selectedTab) {
    setState(() {
      _justTapped = selectedTab;
      _isAnimating[selectedTab] = true;
    });

    // Navigate to the selected tab
    switch (selectedTab) {
      case TabItem.shop:
        context.go('/shop');
        break;
      case TabItem.card:
        context.go('/card');
        break;
      case TabItem.luckyDraw:
        context.go('/lucky-draw');
        break;
      case TabItem.blindBox:
        context.go('/blind-box');
        break;
      case TabItem.me:
        context.go('/profile');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentTab = _getCurrentTab(context);
    
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: TabItem.values.indexOf(currentTab),
        onTap: (index) {
          final selected = TabItem.values[index];
          _onTabSelected(selected);
        },
        selectedItemColor: Colors.amber,
        unselectedItemColor: const Color.fromRGBO(153, 153, 153, 1),
        items: TabItem.values.map((tab) {
          final isSelected = currentTab == tab;
          final assetBase = tab.assetName;

          final String iconPath =
              'assets/icons/nav/${assetBase}${isSelected ? 'Active' : 'Inactive'}.svg';

          final String lottiePath =
              'assets/lottie/IconAnimation/${assetBase}.json';

          return BottomNavigationBarItem(
            label: assetBase[0].toUpperCase() + assetBase.substring(1),
            icon: _isAnimating[tab] == true
                ? Lottie.asset(
                    lottiePath,
                    width: 30,
                    height: 30,
                    repeat: false,
                    errorBuilder: (context, error, stackTrace) => const Icon(
                      Icons.error,
                      color: Colors.red,
                      size: 28,
                    ),
                    onLoaded: (composition) {
                      Future.delayed(composition.duration, () {
                        if (mounted) {
                          setState(() => _isAnimating[tab] = false);
                        }
                      });
                    },
                  )
                : MouseRegion(
                    onEnter: (_) => setState(() => _hoverStates[tab] = true),
                    onExit: (_) => setState(() => _hoverStates[tab] = false),
                    child: SvgPicture.asset(
                      iconPath,
                      width: 28,
                      height: 28,
                      color: isSelected
                          ? Colors.amber
                          : _hoverStates[tab] == true
                              ? Colors.amber.shade200
                              : const Color(0xFF999999),
                    ),
                  ),
          );
        }).toList(),
      ),
    );
  }
}
