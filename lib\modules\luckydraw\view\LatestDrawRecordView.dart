import 'package:flutter/material.dart';
import 'package:luckymall/modules/luckydraw/widgets/DrawRecordCard.dart';
import 'package:luckymall/modules/luckydraw/widgets/customAppBar.dart';

class LatestDrawRecordView extends StatelessWidget {
  const LatestDrawRecordView({super.key});

  @override
  Widget build(BuildContext context) {
    // Sample data matching the design
    final List<Map<String, dynamic>> drawRecords = [
      {
        'username': '456456',
        'productTitle':
            'Mouse Razer Pulsar X120 With Ergonomic Functional (PAW3360 Sensor)',
        'date': '2025-06-30',
        'time': '18:30',
        'period': '3',
        'totalShares': '115',
        'batchNumber': '003',
        'participationPoint': '600',
        'winningName': 'Dx*****DX',
        'imageUrl':
            'https://www.flashgadgets.com.my/media/catalog/product/cache/75702c449a202b44dcc72e0c4695f594/_/_/__8886419334033.jpg',
      },
      {
        'username': '789123',
        'productTitle': 'Gaming Keyboard Mechanical RGB Backlit',
        'date': '2025-06-29',
        'time': '16:45',
        'period': '2',
        'totalShares': '89',
        'batchNumber': '002',
        'participationPoint': '450',
        'winningName': 'Ga*****mR',
        'imageUrl':
            'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=400&h=300&fit=crop',
      },
      {
        'username': '321654',
        'productTitle':
            'Wireless Earbuds Pro Max With Active Noise Cancellation',
        'date': '2025-06-28',
        'time': '14:20',
        'period': '1',
        'totalShares': '156',
        'batchNumber': '001',
        'participationPoint': '750',
        'winningName': 'Mu*****ic',
        'imageUrl':
            'https://images.unsplash.com/photo-1590658268037-6bf12165a8df?w=400&h=300&fit=crop',
      },
    ];

    return Scaffold(
      appBar: const CustomAppBar(title: 'Draw Records'),
      backgroundColor: Color(0xFFDEDEDE),
      body: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 16),
        itemCount: drawRecords.length,
        itemBuilder: (context, index) {
          final record = drawRecords[index];
          return DrawRecordCard(
            username: record['username'],
            productTitle: record['productTitle'],
            date: record['date'],
            time: record['time'],
            period: record['period'],
            totalShares: record['totalShares'],
            batchNumber: record['batchNumber'],
            participationPoint: record['participationPoint'],
            winningName: record['winningName'],
            imageUrl: record['imageUrl'],
          );
        },
      ),
    );
  }
}
