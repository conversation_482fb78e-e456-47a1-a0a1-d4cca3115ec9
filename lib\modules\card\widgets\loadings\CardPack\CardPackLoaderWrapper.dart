import 'package:flutter/material.dart';
import '../../../view/CardPackPage.dart';
import 'CardPackLoader.dart';

class CardPackLoaderWrapper extends StatefulWidget {
  const CardPackLoaderWrapper({super.key});

  @override
  State<CardPackLoaderWrapper> createState() => _CardPackLoaderWrapperState();
}

class _CardPackLoaderWrapperState extends State<CardPackLoaderWrapper> {
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return isLoading ? const CardPackLoader() : const CardPackPage();
  }
}
