import 'package:flutter/material.dart';
import '../view/BlindboxShopView.dart';

class BlindBoxBanner extends StatelessWidget {
  const BlindBoxBanner({super.key});

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)

    // Clamp the scale factor to reasonable bounds
    scaleFactor = scaleFactor.clamp(0.8, 1.4);

    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return basePadding; // Small devices
    } else if (screenWidth < 900) {
      return basePadding * 1.2; // Medium devices
    } else {
      return basePadding * 1.5; // Large devices
    }
  }

  // Helper method to get responsive banner height
  double getResponsiveBannerHeight(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return 130; // Small devices
    } else if (screenWidth < 900) {
      return 150; // Medium devices
    } else {
      return 170; // Large devices
    }
  }

  // Helper method to get responsive image size
  double getResponsiveImageSize(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return 130; // Small devices
    } else if (screenWidth < 900) {
      return 150; // Medium devices
    } else {
      return 170; // Large devices
    }
  }

  @override
  Widget build(BuildContext context) {
    final bannerHeight = getResponsiveBannerHeight(context);
    final imageSize = getResponsiveImageSize(context);

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: getResponsivePadding(15, context),
        vertical: getResponsivePadding(1, context),
      ),
      height: bannerHeight,
      decoration: BoxDecoration(
        color: const Color(0xFF1B1464), // deep blue
        borderRadius: BorderRadius.circular(5),
      ),
      child: Row(
        children: [
          // Left Side Text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Blind Box Store',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(20, context),
                    fontWeight: FontWeight.w900,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: getResponsivePadding(8, context)),
                Text(
                  'Psst... Want to try your luck?',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: getResponsivePadding(12, context)),
                CustomVisitStoreButton(),
              ],
            ),
          ),

          // Right Side Image (placeholder)
          SizedBox(width: getResponsivePadding(15, context)),
          ClipRRect(
            borderRadius: BorderRadius.circular(15),
            child: Image.asset(
              'assets/images/blindboxBanner.png',
              height: imageSize,
              width: imageSize,
              fit: BoxFit.cover,
            ),
          ),
        ],
      ),
    );
  }
}

class CustomVisitStoreButton extends StatelessWidget {
  const CustomVisitStoreButton({super.key});

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)

    // Clamp the scale factor to reasonable bounds
    scaleFactor = scaleFactor.clamp(0.8, 1.4);

    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return basePadding; // Small devices
    } else if (screenWidth < 900) {
      return basePadding * 1.2; // Medium devices
    } else {
      return basePadding * 1.5; // Large devices
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => BlindboxShopView()),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFFFFEAAB),
          borderRadius: BorderRadius.circular(15),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: getResponsivePadding(10, context),
          vertical: getResponsivePadding(5, context),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Visit Store',
              style: TextStyle(
                fontSize: getResponsiveFontSize(13, context),
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
            SizedBox(width: getResponsivePadding(12, context)),
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.15),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              padding: EdgeInsets.all(getResponsivePadding(4, context)),
              child: Icon(
                Icons.arrow_forward,
                size: getResponsiveFontSize(20, context),
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
