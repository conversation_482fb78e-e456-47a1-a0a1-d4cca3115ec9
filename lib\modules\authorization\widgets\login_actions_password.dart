import 'package:flutter/material.dart';
import 'package:luckymall/modules/authorization/view/OtpLoginView.dart';

class LoginActions extends StatelessWidget {
  final VoidCallback? onForgotPassword;
  final VoidCallback? onOtpLogin;

  const LoginActions({Key? key, this.onForgotPassword, this.onOtpLogin})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap:
                onForgotPassword ??
                () {
                  // Default forgot password action
                },
            child: const Text(
              'Forgot Password?',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ),
          GestureDetector(
            onTap:
                onOtpLogin ??
                () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const OtpLoginView(),
                    ),
                  );
                },
            child: const Text(
              'OTP Login',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
