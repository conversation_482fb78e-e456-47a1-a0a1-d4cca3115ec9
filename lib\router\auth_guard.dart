import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Simple authentication service for demonstration
class AuthService {
  static bool _isLoggedIn = false;
  
  static bool get isLoggedIn => _isLoggedIn;
  
  static void login() {
    _isLoggedIn = true;
  }
  
  static void logout() {
    _isLoggedIn = false;
  }
}

/// Route guard that redirects to login if user is not authenticated
String? authGuard(BuildContext context, GoRouterState state) {
  final isLoggedIn = AuthService.isLoggedIn;
  final isLoginRoute = state.uri.path == '/login' || 
                      state.uri.path == '/register' || 
                      state.uri.path == '/forgot-password';
  
  // If not logged in and not on a login route, redirect to login
  if (!isLoggedIn && !isLoginRoute) {
    return '/login';
  }
  
  // If logged in and on login route, redirect to main app
  if (isLoggedIn && isLoginRoute) {
    return '/shop';
  }
  
  return null; // No redirect needed
}

/// Route guard specifically for profile and sensitive routes
String? profileGuard(BuildContext context, GoRouterState state) {
  final isLoggedIn = AuthService.isLoggedIn;
  
  // Profile routes require authentication
  if (!isLoggedIn && state.uri.path.startsWith('/profile')) {
    return '/login';
  }
  
  return null;
}
