class ShopProduct {
  final String id;
  final String title;
  final String imageUrl;
  final double currentPrice;
  final double? originalPrice;
  final int? discountPercentage;
  final double rating;
  final int soldCount;
  final double? maxSavings;
  final bool isFavorite;
  final String? category;
  final bool groupBuyEnable; // <-- Add this line

  const ShopProduct({
    required this.id,
    required this.title,
    required this.imageUrl,
    required this.currentPrice,
    this.originalPrice,
    this.discountPercentage,
    required this.rating,
    required this.soldCount,
    this.maxSavings,
    this.isFavorite = false,
    this.category,
    this.groupBuyEnable = false, // <-- Add this line
  });

  factory ShopProduct.fromJson(Map<String, dynamic> json) {
    return ShopProduct(
      id: json['id'] as String,
      title: json['title'] as String,
      imageUrl: json['imageUrl'] as String,
      currentPrice: (json['currentPrice'] as num).toDouble(),
      originalPrice: json['originalPrice'] != null 
          ? (json['originalPrice'] as num).toDouble() 
          : null,
      discountPercentage: json['discountPercentage'] as int?,
      rating: (json['rating'] as num).toDouble(),
      soldCount: json['soldCount'] as int,
      maxSavings: json['maxSavings'] != null 
          ? (json['maxSavings'] as num).toDouble() 
          : null,
      isFavorite: json['isFavorite'] as bool? ?? false,
      category: json['category'] as String?,
      groupBuyEnable: json['groupBuyEnable'] as bool? ?? false, // <-- Add this line
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'imageUrl': imageUrl,
      'currentPrice': currentPrice,
      'originalPrice': originalPrice,
      'discountPercentage': discountPercentage,
      'rating': rating,
      'soldCount': soldCount,
      'maxSavings': maxSavings,
      'isFavorite': isFavorite,
      'category': category,
      'groupBuyEnable': groupBuyEnable, // <-- Add this line
    };
  }

  ShopProduct copyWith({
    String? id,
    String? title,
    String? imageUrl,
    double? currentPrice,
    double? originalPrice,
    int? discountPercentage,
    double? rating,
    int? soldCount,
    double? maxSavings,
    bool? isFavorite,
    String? category,
    bool? groupBuyEnable, // <-- Add this line
  }) {
    return ShopProduct(
      id: id ?? this.id,
      title: title ?? this.title,
      imageUrl: imageUrl ?? this.imageUrl,
      currentPrice: currentPrice ?? this.currentPrice,
      originalPrice: originalPrice ?? this.originalPrice,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      rating: rating ?? this.rating,
      soldCount: soldCount ?? this.soldCount,
      maxSavings: maxSavings ?? this.maxSavings,
      isFavorite: isFavorite ?? this.isFavorite,
      category: category ?? this.category,
      groupBuyEnable: groupBuyEnable ?? this.groupBuyEnable, // <-- Add this line
    );
  }
}

class ShopCategory {
  final String id;
  final String name;
  final String icon;
  final bool isSelected;

  const ShopCategory({
    required this.id,
    required this.name,
    required this.icon,
    this.isSelected = false,
  });

  factory ShopCategory.fromJson(Map<String, dynamic> json) {
    return ShopCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      icon: json['icon'] as String,
      isSelected: json['isSelected'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'isSelected': isSelected,
    };
  }

  ShopCategory copyWith({
    String? id,
    String? name,
    String? icon,
    bool? isSelected,
  }) {
    return ShopCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}