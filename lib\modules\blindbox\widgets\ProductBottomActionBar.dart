import 'package:flutter/material.dart';
import '../view/BlindboxGroupBuyView.dart';

enum ProductBottomActionBarType {
  chatAndBuy, // Chat Now + Buy Now
  chatCartBuy, // Chat Now + Add to Cart + Buy Now
  groupBuy, // Group Buy
  custom, // Custom buttons
}

class ProductBottomActionBar extends StatelessWidget {
  final ProductBottomActionBarType type;
  final VoidCallback? onChatPressed;
  final VoidCallback? onAddToCartPressed;
  final VoidCallback? onBuyNowPressed;
  final List<Widget>? customButtons;

  const ProductBottomActionBar({
    super.key,
    this.type = ProductBottomActionBarType.chatAndBuy,
    this.onChatPressed,
    this.onAddToCartPressed,
    this.onBuyNowPressed,
    this.customButtons,
  });

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)

    // Clamp the scale factor to reasonable bounds
    scaleFactor = scaleFactor.clamp(0.8, 1.4);

    return baseSize * scaleFactor;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: getResponsiveFontSize(60, context),
      child: Row(children: _buildButtons(context)),
    );
  }

  List<Widget> _buildButtons(BuildContext context) {
    switch (type) {
      case ProductBottomActionBarType.chatAndBuy:
        return [
          // Chat Now
          Expanded(
            child: SizedBox.expand(
              child: OutlinedButton(
                onPressed: onChatPressed,
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.black,
                  side: const BorderSide(color: Colors.grey),
                  padding: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  textStyle: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.support_agent,
                      size: getResponsiveFontSize(24, context),
                    ),
                    SizedBox(height: getResponsiveFontSize(4, context)),
                    Text(
                      'Chat Now',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Buy Now
          Expanded(
            child: SizedBox.expand(
              child: ElevatedButton(
                onPressed: onBuyNowPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber[600],
                  foregroundColor: Colors.black,
                  padding: EdgeInsets.zero,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  textStyle: TextStyle(
                    fontSize: getResponsiveFontSize(16, context),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                child: Text(
                  'Buy Now',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(16, context),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ];

      case ProductBottomActionBarType.groupBuy:
        return [
          // Chat Now
          Expanded(
            child: SizedBox.expand(
              child: OutlinedButton(
                onPressed: onChatPressed,
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.black,
                  side: const BorderSide(color: Colors.grey),
                  padding: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  textStyle: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.support_agent,
                      size: getResponsiveFontSize(24, context),
                    ),
                    SizedBox(height: getResponsiveFontSize(4, context)),
                    Text(
                      'Chat Now',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Buy Now
          Expanded(
            child: SizedBox.expand(
              child: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) => const BlindboxGroupBuyView()),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber[600],
                  foregroundColor: Colors.black,
                  padding: EdgeInsets.zero,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  textStyle: TextStyle(
                    fontSize: getResponsiveFontSize(16, context),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                child: Text(
                  'Buy and Create A Group Now',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(16, context),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ];

      case ProductBottomActionBarType.chatCartBuy:
        return [
          // Chat Now
          Expanded(
            child: SizedBox.expand(
              child: OutlinedButton(
                onPressed: onChatPressed,
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.black,
                  side: const BorderSide(color: Colors.grey),
                  padding: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  textStyle: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.support_agent,
                      size: getResponsiveFontSize(24, context),
                    ),
                    SizedBox(height: getResponsiveFontSize(4, context)),
                    Text(
                      'Chat Now',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Add to Cart
          Expanded(
            child: SizedBox.expand(
              child: OutlinedButton(
                onPressed: onAddToCartPressed,
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.black,
                  side: const BorderSide(color: Colors.grey),
                  padding: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  textStyle: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.shopping_cart_outlined,
                      size: getResponsiveFontSize(24, context),
                    ),
                    SizedBox(height: getResponsiveFontSize(4, context)),
                    Text(
                      'Add to Cart',
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Buy Now
          Expanded(
            child: SizedBox.expand(
              child: ElevatedButton(
                onPressed: onBuyNowPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber[600],
                  foregroundColor: Colors.black,
                  padding: EdgeInsets.zero,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  textStyle: TextStyle(
                    fontSize: getResponsiveFontSize(16, context),
                    fontWeight: FontWeight.bold,
                  ),
                ),
                child: Text(
                  'Buy Now',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(16, context),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ];

      case ProductBottomActionBarType.custom:
        return customButtons ?? [];
    }
  }
}
