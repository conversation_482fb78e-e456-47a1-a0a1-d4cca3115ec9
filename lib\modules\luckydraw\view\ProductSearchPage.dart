import 'package:flutter/material.dart';
import '../widgets/PointProductCard.dart';
import '../widgets/PointProductDetails.dart';
import '../widgets/SkeletonLoader.dart';

class ProductSearchPage extends StatefulWidget {
  final List<Map<String, dynamic>> allProducts;

  const ProductSearchPage({super.key, required this.allProducts});

  @override
  State<ProductSearchPage> createState() => _ProductSearchPageState();
}

class _ProductSearchPageState extends State<ProductSearchPage> {
  final TextEditingController _searchController = TextEditingController();
  String searchQuery = '';
  List<Map<String, dynamic>> searchResults = [];
  bool isSearching = false;
  bool hasSearched = false;
  List<String> recentSearches = [];

  @override
  void initState() {
    super.initState();
    // Auto focus the search field when page opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(FocusNode());
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) {
      setState(() {
        searchResults = [];
        hasSearched = false;
        isSearching = false;
      });
      return;
    }

    setState(() {
      isSearching = true;
      hasSearched = true;
    });

    // Simulate network delay
    Future.delayed(const Duration(milliseconds: 300), () {
      final results = widget.allProducts.where((product) {
        return product['productName'].toString().toLowerCase().contains(
          query.toLowerCase(),
        );
      }).toList();

      setState(() {
        searchResults = results;
        isSearching = false;
      });

      // Add to recent searches if not already present
      if (!recentSearches.contains(query) && query.trim().isNotEmpty) {
        setState(() {
          recentSearches.insert(0, query);
          if (recentSearches.length > 5) {
            recentSearches = recentSearches.take(5).toList();
          }
        });
      }
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      searchQuery = '';
      searchResults = [];
      hasSearched = false;
      isSearching = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          // Custom search header to match shop UI
          Container(
            color: const Color(0xFFFFE066),
            padding: const EdgeInsets.fromLTRB(20, 50, 20, 16),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: const Icon(
                    Icons.arrow_back,
                    color: Colors.black,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    height: 36,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Padding(
                          padding: EdgeInsets.symmetric(horizontal: 12),
                          child: Icon(
                            Icons.search,
                            color: Colors.grey,
                            size: 20,
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            controller: _searchController,
                            autofocus: true,
                            onChanged: (value) {
                              setState(() {
                                searchQuery = value;
                              });
                              _performSearch(value);
                            },
                            onSubmitted: (value) {
                              _performSearch(value);
                            },
                            decoration: const InputDecoration(
                              hintText: 'Search Products',
                              border: InputBorder.none,
                              isDense: true,
                              contentPadding: EdgeInsets.symmetric(vertical: 8),
                            ),
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () => _performSearch(searchQuery),
                          child: Container(
                            height: 32,
                            margin: const EdgeInsets.symmetric(
                              vertical: 2,
                              horizontal: 4,
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 18),
                            decoration: BoxDecoration(
                              color: Colors.red[700],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Center(
                              child: Text(
                                'Search',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Rest of the body content
          Expanded(
            child: Column(
              children: [
                // Search suggestions or recent searches
                if (!hasSearched && recentSearches.isNotEmpty)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.all(16),
                          child: Text(
                            'Recent Searches',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        Expanded(
                          child: ListView.builder(
                            itemCount: recentSearches.length,
                            itemBuilder: (context, index) {
                              return ListTile(
                                leading: Icon(
                                  Icons.history,
                                  color: Colors.grey[600],
                                ),
                                title: Text(recentSearches[index]),
                                onTap: () {
                                  _searchController.text =
                                      recentSearches[index];
                                  setState(() {
                                    searchQuery = recentSearches[index];
                                  });
                                  _performSearch(recentSearches[index]);
                                },
                                trailing: IconButton(
                                  icon: Icon(
                                    Icons.close,
                                    color: Colors.grey[400],
                                    size: 20,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      recentSearches.removeAt(index);
                                    });
                                  },
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),

                // Search results
                if (hasSearched)
                  Expanded(
                    child: Column(
                      children: [
                        // Results header
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(16),
                          color: Colors.grey[50],
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                isSearching
                                    ? 'Searching...'
                                    : 'Found ${searchResults.length} result${searchResults.length != 1 ? 's' : ''} for "$searchQuery"',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black54,
                                ),
                              ),
                              if (!isSearching && searchResults.isNotEmpty)
                                Icon(
                                  Icons.check_circle,
                                  color: Colors.green,
                                  size: 16,
                                ),
                            ],
                          ),
                        ),

                        // Results content
                        Expanded(
                          child: isSearching
                              ? const PointProductGridSkeleton()
                              : searchResults.isEmpty
                              ? Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.search_off,
                                        size: 64,
                                        color: Colors.grey[400],
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'No products found',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'Try searching with different keywords',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey[500],
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : PointProductGrid(
                                  products: searchResults,
                                  onProductTap: (product) {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            PointProductDetails(
                                              product: product,
                                            ),
                                      ),
                                    );
                                  },
                                ),
                        ),
                      ],
                    ),
                  ),

                // Default state when no search has been performed
                if (!hasSearched && recentSearches.isEmpty)
                  Expanded(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.search, size: 64, color: Colors.grey[400]),
                          const SizedBox(height: 16),
                          Text(
                            'Search for products',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Enter keywords to find point products',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
