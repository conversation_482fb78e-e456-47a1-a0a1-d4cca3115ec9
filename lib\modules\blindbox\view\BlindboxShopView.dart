import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/CategoryTab.dart';
import '../widgets/BlindboxShopCard.dart';
import '../widgets/BlindboxFilterOverlay.dart';
import 'BlindboxBuyView.dart';
import '../view-model/BlindboxShopVM.dart';
import '../view-model/BlindboxCategoryBarVM.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../widgets/LuckyGroupBuyInfoDialog.dart';

class BlindboxShopView extends StatefulWidget {
  const BlindboxShopView({super.key});

  @override
  State<BlindboxShopView> createState() => _BlindboxShopViewState();
}

class _BlindboxShopViewState extends State<BlindboxShopView> {
  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)
    // Clamp the scale factor to reasonable bounds
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding; // Small devices
    } else if (screenWidth < 900) {
      return basePadding * 1.2; // Medium devices
    } else {
      return basePadding * 1.5; // Large devices
    }
  }

  // Helper method to get responsive crossAxisCount for grid
  int getResponsiveCrossAxisCount(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 2; // Small devices (phones)
    } else if (screenWidth < 900) {
      return 3; // Medium devices (tablets)
    } else if (screenWidth < 1200) {
      return 4; // Large tablets
    } else {
      return 5; // Desktop/large screens
    }
  }

  void _showFilterOverlay(BuildContext context, BlindboxShopViewVM vm) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BlindboxFilterOverlay(
        initialFilters: vm.currentFilters,
        onFilterApplied: vm.applyFilters,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final crossAxisCount = getResponsiveCrossAxisCount(context);

    return ChangeNotifierProvider(
      create: (_) => BlindboxShopViewVM(),
      child: Consumer<BlindboxShopViewVM>(
        builder: (context, vm, _) {
          return Scaffold(
            backgroundColor: Colors.white,
            appBar: AppBar(
              backgroundColor: Color(0xFFFCD255),
              elevation: 0,
              toolbarHeight: 0,
            ),
            body: Column(
              children: [
                Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Color(0xFFFCD255),
                        Color(0xFFFCD255),
                        Color(0xFFFFEAAB),
                        Color(0xFFFFEAAB),
                        Color(0xFFFFFFFF),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      stops: [0.0, 0.36, 0.67, 0.77, 1.0],
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: getResponsivePadding(16.0, context),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Container(
                                height: getResponsivePadding(44, context),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: getResponsivePadding(
                                          12,
                                          context,
                                        ),
                                      ),
                                      child: Icon(
                                        Icons.search,
                                        color: Colors.grey,
                                        size: getResponsiveFontSize(
                                          20,
                                          context,
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: TextField(
                                        controller: vm.searchController,
                                        decoration: InputDecoration(
                                          hintText: 'Search',
                                          border: InputBorder.none,
                                          isDense: true,
                                          contentPadding: EdgeInsets.symmetric(
                                            vertical: getResponsivePadding(
                                              10,
                                              context,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                        right: getResponsivePadding(4, context),
                                      ),
                                      child: Material(
                                        color: Colors.amber,
                                        borderRadius: BorderRadius.circular(8),
                                        child: InkWell(
                                          borderRadius: BorderRadius.circular(
                                            8,
                                          ),
                                          onTap: () =>
                                              vm.navigateToSearch(context),
                                          child: Padding(
                                            padding: EdgeInsets.symmetric(
                                              horizontal: getResponsivePadding(
                                                16,
                                                context,
                                              ),
                                              vertical: getResponsivePadding(
                                                8,
                                                context,
                                              ),
                                            ),
                                            child: Text(
                                              'Search',
                                              style: TextStyle(
                                                color: Colors.black,
                                                fontWeight: FontWeight.bold,
                                                fontSize: getResponsiveFontSize(
                                                  14,
                                                  context,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(width: getResponsivePadding(12, context)),
                            IconButton(
                              icon: Icon(
                                Icons.favorite,
                                color: Colors.black,
                                size: getResponsiveFontSize(28, context),
                              ),
                              onPressed: () {},
                            ),
                            IconButton(
                              icon: Icon(
                                Icons.shopping_cart,
                                color: Colors.black,
                                size: getResponsiveFontSize(28, context),
                              ),
                              onPressed: () {},
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: getResponsivePadding(10, context)),
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: getResponsivePadding(16.0, context),
                        ),
                        child: Row(
                          children: [
                            IconButton(
                              icon: Icon(
                                Icons.arrow_back_ios_new,
                                size: getResponsiveFontSize(28, context),
                                color: Colors.black,
                              ),
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                            ),
                            const SizedBox(width: 4),
                            const Text(
                              "Lucky Mall",
                              style: TextStyle(
                                fontSize: 32,
                                fontWeight: FontWeight.w900,
                                color: Colors.black,
                              ),
                            ),
                            const Spacer(),
                            Material(
                              color: Colors.amber,
                              borderRadius: BorderRadius.circular(12),
                              child: InkWell(
                                borderRadius: BorderRadius.circular(12),
                                onTap: () => _showFilterOverlay(context, vm),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 14,
                                    vertical: 8,
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.tune,
                                        size: 20,
                                        color: Colors.black,
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        'Price Range',
                                        style: TextStyle(
                                          color: Colors.black,
                                          fontWeight: FontWeight.w500,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: getResponsivePadding(10, context)),
                      // Category Tabs inside gradient
                      ChangeNotifierProvider(
                        create: (_) => BlindboxCategoryBarVM(),
                        child: Consumer<BlindboxCategoryBarVM>(
                          builder: (context, catVM, _) {
                            // Get the parent BlindboxShopViewVM
                            final shopVM = Provider.of<BlindboxShopViewVM>(
                              context,
                              listen: false,
                            );
                            return Column(
                              children: [
                                SingleChildScrollView(
                                  scrollDirection: Axis.horizontal,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: catVM.categories.map((cat) {
                                      return GestureDetector(
                                        onTap: () {
                                          catVM.selectCategory(cat.id);
                                          // Map category id to product category name
                                          String? categoryName;
                                          switch (cat.id) {
                                            case 'all':
                                              categoryName = 'All';
                                              break;
                                            case 'group':
                                              categoryName = 'Group Order';
                                              break;
                                            case 'travel':
                                              categoryName = 'Travel';
                                              break;
                                            case 'stationery':
                                              categoryName = 'Stationery';
                                              break;
                                            case 'sports':
                                              categoryName = 'Sports';
                                              break;
                                            case 'snacks':
                                              categoryName = 'Snacks';
                                              break;
                                            case 'pet':
                                              categoryName = 'Pet';
                                              break;
                                            case 'kitchen':
                                              categoryName = 'Kitchen';
                                              break;
                                            case 'household':
                                              categoryName = 'Household';
                                              break;
                                            case 'health':
                                              categoryName = 'Health';
                                              break;
                                            case 'fashion':
                                              categoryName = 'Fashion';
                                              break;
                                            case 'electronic_device':
                                              categoryName =
                                                  'Electronic Device';
                                              break;
                                            case 'electronic_accessories':
                                              categoryName =
                                                  'Electronic Accessories';
                                              break;
                                            case 'beauty':
                                              categoryName = 'Beauty';
                                              break;
                                            default:
                                              categoryName = null;
                                          }
                                          // Update the shop VM's filters
                                          final currentFilters =
                                              Map<String, dynamic>.from(
                                                shopVM.currentFilters ?? {},
                                              );
                                          currentFilters['category'] =
                                              (categoryName == 'All')
                                              ? null
                                              : categoryName;
                                          shopVM.applyFilters(currentFilters);
                                        },
                                        child: CategoryTab(
                                          icon: cat.icon,
                                          iconAsset: cat.iconAsset,
                                          label: cat.name,
                                          isSelected: cat.isSelected,
                                        ),
                                      );
                                    }).toList(),
                                  ),
                                ),
                                if (catVM.selectedCategoryId == 'group')
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: getResponsivePadding(
                                        16,
                                        context,
                                      ),
                                      vertical: getResponsiveFontSize(
                                        8,
                                        context,
                                      ),
                                    ),
                                    child: GestureDetector(
                                      onTap: () {
                                        showDialog(
                                          context: context,
                                          builder: (context) =>
                                              const LuckyGroupBuyInfoDialog(),
                                        );
                                      },
                                      child: Container(
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                          color: Colors.transparent,
                                          borderRadius: BorderRadius.circular(
                                            getResponsiveFontSize(12, context),
                                          ),
                                        ),
                                        padding: EdgeInsets.symmetric(
                                          horizontal: getResponsiveFontSize(
                                            12,
                                            context,
                                          ),
                                          vertical: getResponsiveFontSize(
                                            8,
                                            context,
                                          ),
                                        ),
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            Container(
                                              width: getResponsiveFontSize(
                                                28,
                                                context,
                                              ),
                                              height: getResponsiveFontSize(
                                                28,
                                                context,
                                              ),
                                              decoration: BoxDecoration(
                                                color: Colors.red[600],
                                                shape: BoxShape.circle,
                                              ),
                                              child: Center(
                                                child: Text(
                                                  '?',
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontWeight: FontWeight.bold,
                                                    fontSize:
                                                        getResponsiveFontSize(
                                                          20,
                                                          context,
                                                        ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            SizedBox(
                                              width: getResponsiveFontSize(
                                                10,
                                                context,
                                              ),
                                            ),
                                            Text(
                                              'What is Lucky Group Buy?',
                                              style: TextStyle(
                                                color: Colors.red[600],
                                                fontWeight: FontWeight.w600,
                                                fontSize: getResponsiveFontSize(
                                                  16,
                                                  context,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                // Mystery Boxes Grid
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: getResponsivePadding(16, context),
                    ),
                    child: vm.isLoading
                        ? GridView.builder(
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: crossAxisCount,
                                  mainAxisSpacing: getResponsivePadding(
                                    16,
                                    context,
                                  ),
                                  crossAxisSpacing: getResponsivePadding(
                                    16,
                                    context,
                                  ),
                                  childAspectRatio: 0.8,
                                ),
                            itemCount: 8,
                            itemBuilder: (context, index) =>
                                const BlindboxShopCardShimmer(),
                          )
                        : vm.filteredProducts.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const SizedBox(height: 40),
                                SvgPicture.asset(
                                  'assets/icons/Empty Blind Box.svg',
                                  width: 160,
                                  height: 160,
                                  colorFilter: ColorFilter.mode(
                                    Colors.grey,
                                    BlendMode.srcIn,
                                  ),
                                ),
                                const SizedBox(height: 32),
                                const Text(
                                  'No products found on this category',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 18,
                                    color: Colors.grey,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ],
                            ),
                          )
                        : GridView.builder(
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: crossAxisCount,
                                  mainAxisSpacing: getResponsivePadding(
                                    16,
                                    context,
                                  ),
                                  crossAxisSpacing: getResponsivePadding(
                                    16,
                                    context,
                                  ),
                                  childAspectRatio: 0.8,
                                ),
                            itemCount: vm.filteredProducts.length,
                            itemBuilder: (context, index) {
                              final product = vm.filteredProducts[index];
                              final isGroupBuy = product['isGroupBuy'] == true;

                              return BlindboxShopCard(
                                title: product['title'] as String,
                                price: product['price'] as String,
                                rating: product['rating'] as double,
                                soldCount: product['soldCount'] as int,
                                maxSavings: product['maxSavings'] as String?,
                                imageUrl: product['imageUrl'] as String?,
                                isGroupBuy: isGroupBuy,
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => BlindboxBuyView(
                                        isGroupBuy: isGroupBuy,
                                        product: product,
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                          ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
