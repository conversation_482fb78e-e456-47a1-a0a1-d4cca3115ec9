import 'package:flutter/material.dart';
import 'package:luckymall/modules/authorization/widgets/login_header.dart';
import 'package:luckymall/modules/authorization/widgets/phone_number_field.dart';
import 'package:luckymall/modules/authorization/widgets/otp_field.dart';
import 'package:luckymall/modules/authorization/widgets/login_actions_otp.dart';
import 'package:luckymall/modules/authorization/widgets/login_button.dart';
import 'package:luckymall/modules/authorization/widgets/register_prompt.dart';
import 'package:luckymall/modules/authorization/view/RegisterView.dart';

class OtpLoginView extends StatefulWidget {
  const OtpLoginView({Key? key}) : super(key: key);

  @override
  State<OtpLoginView> createState() => _OtpLoginViewState();
}

class _OtpLoginViewState extends State<OtpLoginView> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _otpController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  void _handleLogin() {
    setState(() {
      _isLoading = true;
    });

    // Simulate OTP verification process
    Future.delayed(const Duration(seconds: 2), () {
      setState(() {
        _isLoading = false;
      });
      // Add your OTP verification logic here
    });
  }

  void _handleResendOtp() {
    // Navigate to resend OTP screen or handle resend logic
    // Navigator.push(context, MaterialPageRoute(builder: (context) => ResendOtpView()));
  }

  void _handleRegister() {
    // Navigate to register screen
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const RegisterView()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFFFFFFF), Color(0xFFFCD255)],
          ),
        ),
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height,
            ),
            child: IntrinsicHeight(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const LoginHeader(),
                  PhoneNumberField(
                    controller: _phoneController,
                    onChanged: (value) {
                      // Handle phone number changes
                    },
                  ),
                  const SizedBox(height: 20),
                  OtpField(
                    controller: _otpController,
                    onChanged: (value) {
                      // Handle OTP changes
                    },
                  ),
                  const SizedBox(height: 12),
                  LoginActions(onForgotPassword: _handleResendOtp),
                  const SizedBox(height: 24),
                  LoginButton(onPressed: _handleLogin, isLoading: _isLoading),
                  const SizedBox(height: 24),
                  RegisterPrompt(onRegisterTap: _handleRegister),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
