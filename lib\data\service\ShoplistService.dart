import '../model/ShoplistModel.dart';

class ShopService {
  // Mock data for demonstration
  static List<ShopProduct> getMockProducts() {
    return [
      ShopProduct(
        id: '1',
        title: '<PERSON><PERSON><PERSON> & <PERSON>\'s Classic All- In-One Pot Wi...',
        imageUrl: 'https://images.unsplash.com/photo-1584990347498-08b2c87b4c73?w=400&h=300&fit=crop',
        currentPrice: 369.00,
        originalPrice: null,
        discountPercentage: null,
        rating: 4.6,
        soldCount: 15,
        maxSavings: 50.0,
        category: 'electronic',
        groupBuyEnable: true, // <-- Add this line
      ),
      ShopProduct(
        id: '2',
        title: '<PERSON>\'s 3D Visible Window Air Fryer',
        imageUrl: 'https://images.unsplash.com/photo-1574269909862-7e1d70bb8078?w=400&h=300&fit=crop',
        currentPrice: 175.99,
        originalPrice: 219.99,
        discountPercentage: 20,
        rating: 4.9,
        soldCount: 26,
        maxSavings: 10.0,
        category: 'electronic',
        groupBuyEnable: false, // <-- Add this line
      ),
      ShopProduct(
        id: '3',
        title: '<PERSON> Frying Pan 30cm Non-stick pan Kitchen Fryi...',
        imageUrl: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop',
        currentPrice: 54.00,
        originalPrice: null,
        discountPercentage: null,
        rating: 4.6,
        soldCount: 15,
        maxSavings: 15.0,
        category: 'kitchen',
      ),
      ShopProduct(
        id: '4',
        title: 'F&F : Omar Bookshelf / kabinet buku tall/wo...',
        imageUrl: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
        currentPrice: 56.00,
        originalPrice: 70.00,
        discountPercentage: 20,
        rating: 5.0,
        soldCount: 26,
        maxSavings: 10.0,
        category: 'furniture',
      ),
      ShopProduct(
        id: '5',
        title: 'Apple iPhone 15 Pro Max 256GB',
        imageUrl: 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400&h=300&fit=crop',
        currentPrice: 5899.00,
        originalPrice: 6299.00,
        discountPercentage: 6,
        rating: 4.8,
        soldCount: 120,
        maxSavings: 400.0,
        category: 'electronic_device',
        groupBuyEnable: false,
      ),
      ShopProduct(
        id: '6',
        title: 'Samsung Galaxy Buds2 Pro Wireless Earbuds',
        imageUrl: 'https://images.unsplash.com/photo-1517336714731-489689fd1ca8?w=400&h=300&fit=crop',
        currentPrice: 799.00,
        originalPrice: 999.00,
        discountPercentage: 20,
        rating: 4.7,
        soldCount: 80,
        maxSavings: 200.0,
        category: 'electronic_accessories',
        groupBuyEnable: true,
      ),
      ShopProduct(
        id: '7',
        title: 'Women\'s Summer Floral Dress',
        imageUrl: 'https://images.unsplash.com/photo-1512436991641-6745cdb1723f?w=400&h=300&fit=crop',
        currentPrice: 129.00,
        originalPrice: 159.00,
        discountPercentage: 19,
        rating: 4.5,
        soldCount: 45,
        maxSavings: 30.0,
        category: 'fashion',
        groupBuyEnable: false,
      ),
      ShopProduct(
        id: '8',
        title: 'Men\'s Classic Analog Watch',
        imageUrl: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?w=400&h=300&fit=crop',
        currentPrice: 299.00,
        originalPrice: 399.00,
        discountPercentage: 25,
        rating: 4.3,
        soldCount: 60,
        maxSavings: 100.0,
        category: 'fashion',
        groupBuyEnable: true,
      ),
      ShopProduct(
        id: '9',
        title: 'L\'Oreal Paris Revitalift Day Cream',
        imageUrl: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?w=400&h=300&fit=crop',
        currentPrice: 89.00,
        originalPrice: 109.00,
        discountPercentage: 18,
        rating: 4.9,
        soldCount: 200,
        maxSavings: 20.0,
        category: 'beauty',
        groupBuyEnable: false,
      ),
      ShopProduct(
        id: '10',
        title: 'Dyson V11 Cordless Vacuum Cleaner',
        imageUrl: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?w=400&h=300&fit=crop',
        currentPrice: 2499.00,
        originalPrice: 2999.00,
        discountPercentage: 17,
        rating: 4.8,
        soldCount: 35,
        maxSavings: 500.0,
        category: 'electronic_device',
        groupBuyEnable: true,
      ),
      ShopProduct(
        id: '11',
        title: 'Minimalist Wooden Study Desk',
        imageUrl: 'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?w=400&h=300&fit=crop',
        currentPrice: 399.00,
        originalPrice: 499.00,
        discountPercentage: 20,
        rating: 4.6,
        soldCount: 22,
        maxSavings: 100.0,
        category: 'furniture',
        groupBuyEnable: false,
      ),
      ShopProduct(
        id: '12',
        title: 'Philips Air Purifier Series 2000',
        imageUrl: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?w=400&h=300&fit=crop',
        currentPrice: 899.00,
        originalPrice: 1099.00,
        discountPercentage: 18,
        rating: 4.7,
        soldCount: 50,
        maxSavings: 200.0,
        category: 'electronic_device',
        groupBuyEnable: true,
      ),
      ShopProduct(
        id: '13',
        title: 'KitchenAid Stand Mixer 4.8L',
        imageUrl: 'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=400&h=300&fit=crop',
        currentPrice: 2299.00,
        originalPrice: 2599.00,
        discountPercentage: 12,
        rating: 4.9,
        soldCount: 18,
        maxSavings: 300.0,
        category: 'kitchen',
        groupBuyEnable: false,
      ),
      ShopProduct(
        id: '14',
        title: 'Ergonomic Mesh Office Chair',
        imageUrl: 'https://images.unsplash.com/photo-1519710164239-da123dc03ef4?w=400&h=300&fit=crop',
        currentPrice: 499.00,
        originalPrice: 599.00,
        discountPercentage: 17,
        rating: 4.4,
        soldCount: 40,
        maxSavings: 100.0,
        category: 'furniture',
        groupBuyEnable: true,
      ),
    ];
  }

  static List<ShopCategory> getMockCategories() {
    return [
      const ShopCategory(
        id: 'all',
        name: 'All',
        icon: 'grid_view.svg',
        isSelected: true,
      ),
      const ShopCategory(
        id: 'travel',
        name: 'Travel',
        icon: 'Travel.svg',
      ),
      const ShopCategory(
        id: 'stationery',
        name: 'Stationery',
        icon: 'Stationery.svg',
      ),
      const ShopCategory(
        id: 'sports',
        name: 'Sports',
        icon: 'Sports.svg',
      ),
      const ShopCategory(
        id: 'snacks',
        name: 'Snacks',
        icon: 'Snacks.svg',
      ),
      const ShopCategory(
        id: 'pet',
        name: 'Pet',
        icon: 'Pet.svg',
      ),
      const ShopCategory(
        id: 'kitchen',
        name: 'Kitchen',
        icon: 'Kitchen.svg',
      ),
      const ShopCategory(
        id: 'household',
        name: 'Household',
        icon: 'Household.svg',
      ),
      const ShopCategory(
        id: 'health',
        name: 'Health',
        icon: 'Health.svg',
      ),
      const ShopCategory(
        id: 'group',
        name: 'Group Order',
        icon: 'Group Order.svg',
      ),
      const ShopCategory(
        id: 'fashion',
        name: 'Fashion',
        icon: 'Fashion.svg',
      ),
      const ShopCategory(
        id: 'electronic_device',
        name: 'Electronic Device',
        icon: 'Electronic Device.svg',
      ),
      const ShopCategory(
        id: 'electronic_accessories',
        name: 'Electronic Accessories',
        icon: 'Electronic Accessories.svg',
      ),
      const ShopCategory(
        id: 'beauty',
        name: 'Beauty',
        icon: 'Beauty.svg',
      ),
    ];
  }

  // Future methods for API calls
  static Future<List<ShopProduct>> fetchProducts() async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 500));
    return getMockProducts();
  }

  static Future<List<ShopCategory>> fetchCategories() async {
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 300));
    return getMockCategories();
  }

  static Future<List<ShopProduct>> searchProducts(String query) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final products = getMockProducts();
    return products
        .where((product) =>
            product.title.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  static Future<List<ShopProduct>> getProductsByCategory(String categoryId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    final products = getMockProducts();
    if (categoryId == 'all') {
      return products;
    }
    if (categoryId == 'group') {
      // Filter by groupBuyEnable
      return products.where((product) => product.groupBuyEnable).toList();
    }
    // Filter by category
    return products.where((product) => product.category == categoryId).toList();
  }
}