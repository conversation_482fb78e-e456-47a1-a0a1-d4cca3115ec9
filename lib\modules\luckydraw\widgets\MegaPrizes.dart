import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class MegaPrize {
  final String name;
  final String imageUrl;
  final int points;
  final int position;

  MegaPrize({
    required this.name,
    required this.imageUrl,
    required this.points,
    required this.position,
  });
}

class MegaPrizes extends StatelessWidget {
  final List<MegaPrize> prizes;
  final VoidCallback? onPrizeJoin;

  const MegaPrizes({Key? key, required this.prizes, this.onPrizeJoin})
    : super(key: key);

  // Default prizes data if none provided
  static List<MegaPrize> get defaultPrizes => [
    MegaPrize(
      name: "Japanese Wagyu Sirloin",
      imageUrl:
          "https://www.wagyu.my/wp-content/uploads/2020/11/JAPANESE-WAGYU-SIRLOIN.png",
      points: 20000,
      position: 1,
    ),
    MegaPrize(
      name: "Necklace Diamond Aura",
      imageUrl:
          "https://media.hswstatic.com/eyJidWNrZXQiOiJjb250ZW50Lmhzd3N0YXRpYy5jb20iLCJrZXkiOiJnaWZcL01vc3QtZXhwZW5zaXZlLTIuanBnIiwiZWRpdHMiOnsicmVzaXplIjp7IndpZHRoIjo4Mjh9fX0=",
      points: 20000,
      position: 2,
    ),
    MegaPrize(
      name: "Sawit Fruit Bunches",
      imageUrl:
          "https://i0.wp.com/sabahmedia.com/wp-content/uploads/2022/09/PIX-MPOB-TEKNOLOGI_1648628803.webp?fit=960%2C637&ssl=1",
      points: 20000,
      position: 3,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final displayPrizes = prizes.isNotEmpty ? prizes : defaultPrizes;
    final screenWidth = MediaQuery.of(context).size.width;
    double scale(double value) => value * screenWidth / 375;
    double clampW(double value, double min, double max) =>
        value.clamp(min, max);

    // Responsive sizes with larger max for big screens
    final double cardWidth = clampW(scale(100), 80, 180); // was 120
    final double cardImageHeight = clampW(scale(70), 50, 140); // was 90
    final double cardImageWidth = clampW(scale(90), 60, 160); // was 110
    final double podiumWidth = clampW(scale(100), 70, 180); // was 120
    final double podiumHeight1 = clampW(scale(105), 70, 200); // was 130
    final double podiumHeight2 = clampW(scale(85), 60, 170); // was 110
    final double podiumHeight3 = clampW(scale(70), 50, 140); // was 100
    final double badgeSize = clampW(scale(35), 24, 60); // was 40
    final double joinBtnHeight = clampW(scale(22), 16, 40); // was 28
    final double joinFontSize = clampW(scale(9), 8, 18); // was 12
    final double titleFontSize = clampW(scale(35), 20, 48); // was 38
    final double prizeFontSize = clampW(scale(10), 8, 20); // was 13
    final double pointsFontSize = clampW(scale(10), 8, 20); // was 13
    final double progressBarHeight = clampW(scale(4), 2, 10); // was 6
    final double progressBarRadius = clampW(scale(2), 1, 6); // was 3
    final double iconSize1 = clampW(scale(24), 16, 36); // was 28
    final double iconSize2 = clampW(scale(20), 14, 32); // was 24
    final double iconSize3 = clampW(scale(18), 12, 28); // was 22
    final double fontSize1 = clampW(scale(20), 12, 28); // was 22
    final double fontSize2 = clampW(scale(18), 10, 24); // was 20
    final double fontSize3 = clampW(scale(14), 8, 20); // was 16

    // Calculate total height needed for tallest card + tallest podium + paddings
    final double tallestCardHeight =
        cardImageHeight +
        (cardWidth * 0.06 * 2) +
        prizeFontSize * 2 +
        pointsFontSize * 2 +
        progressBarHeight * 2 +
        badgeSize * 0.7;
    final double totalHeight = tallestCardHeight + podiumHeight1;
    // Remove fixed height for podium area to allow natural sizing
    final double? podiumAreaHeight = null;

    return Container(
      margin: EdgeInsets.all(clampW(scale(10), 6, 24)),
      decoration: BoxDecoration(
        color: const Color(0xFFD90019),
        border: Border.all(
          color: const Color(0xFFFF4C61),
          width: clampW(scale(5), 3, 12),
        ),
        borderRadius: BorderRadius.circular(clampW(scale(20), 12, 40)),
      ),
      child: Padding(
        padding: EdgeInsets.fromLTRB(
          clampW(scale(8), 4, 24),
          0, // Remove top padding
          clampW(scale(8), 4, 24),
          0, // Keep bottom padding at 0
        ),
        child: Column(
          children: [
            // Title
            Text(
              'MEGA PRIZES',
              style: TextStyle(
                fontSize: titleFontSize,
                fontWeight: FontWeight.bold,
                fontFamily: 'Source Serif Pro',
                color: Colors.white,
                letterSpacing: 2.5,
              ),
            ),
            // Custom Podium with prizes
            Transform.translate(
              offset: Offset(0, 0), // No vertical offset
              child: SizedBox(
                // No fixed height, let content size naturally
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // 2nd place (left)
                    Expanded(
                      child: _buildPodiumColumn(
                        prize: displayPrizes[1],
                        podiumHeight: podiumHeight2,
                        podiumColor: const Color(0xFFFFBF00),
                        position: 2,
                        icon: Icons.military_tech,
                        iconSize: iconSize2,
                        fontSize: fontSize2,
                        cardWidth: cardWidth,
                        cardImageHeight: cardImageHeight,
                        cardImageWidth: cardImageWidth,
                        badgeSize: badgeSize,
                        joinBtnHeight: joinBtnHeight,
                        joinFontSize: joinFontSize,
                        prizeFontSize: prizeFontSize,
                        pointsFontSize: pointsFontSize,
                        progressBarHeight: progressBarHeight,
                        progressBarRadius: progressBarRadius,
                        context: context,
                        onJoin: onPrizeJoin,
                      ),
                    ),
                    SizedBox(width: clampW(scale(6), 2, 20)),
                    // 1st place (center - tallest)
                    Expanded(
                      child: _buildPodiumColumn(
                        prize: displayPrizes[0],
                        podiumHeight: podiumHeight1,
                        podiumColor: const Color(0xFFFFBF00),
                        position: 1,
                        icon: Icons.emoji_events,
                        iconSize: iconSize1,
                        fontSize: fontSize1,
                        cardWidth: cardWidth,
                        cardImageHeight: cardImageHeight,
                        cardImageWidth: cardImageWidth,
                        badgeSize: badgeSize,
                        joinBtnHeight: joinBtnHeight,
                        joinFontSize: joinFontSize,
                        prizeFontSize: prizeFontSize,
                        pointsFontSize: pointsFontSize,
                        progressBarHeight: progressBarHeight,
                        progressBarRadius: progressBarRadius,
                        context: context,
                        onJoin: onPrizeJoin,
                      ),
                    ),
                    SizedBox(width: clampW(scale(6), 2, 20)),
                    // 3rd place (right - shortest)
                    Expanded(
                      child: _buildPodiumColumn(
                        prize: displayPrizes[2],
                        podiumHeight: podiumHeight3,
                        podiumColor: const Color(0xFFFFBF00),
                        position: 3,
                        icon: Icons.workspace_premium,
                        iconSize: iconSize3,
                        fontSize: fontSize3,
                        cardWidth: cardWidth,
                        cardImageHeight: cardImageHeight,
                        cardImageWidth: cardImageWidth,
                        badgeSize: badgeSize,
                        joinBtnHeight: joinBtnHeight,
                        joinFontSize: joinFontSize,
                        prizeFontSize: prizeFontSize,
                        pointsFontSize: pointsFontSize,
                        progressBarHeight: progressBarHeight,
                        progressBarRadius: progressBarRadius,
                        context: context,
                        onJoin: onPrizeJoin,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPodiumColumn({
    required MegaPrize prize,
    required double podiumHeight,
    required Color podiumColor,
    required int position,
    required IconData icon,
    required double iconSize,
    required double fontSize,
    required double cardWidth,
    required double cardImageHeight,
    required double cardImageWidth,
    required double badgeSize,
    required double joinBtnHeight,
    required double joinFontSize,
    required double prizeFontSize,
    required double pointsFontSize,
    required double progressBarHeight,
    required double progressBarRadius,
    required BuildContext context,
    required VoidCallback? onJoin,
  }) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Prize card on top
        _buildPrizeCard(
          prize,
          context,
          cardWidth,
          cardImageHeight,
          cardImageWidth,
          badgeSize,
          prizeFontSize,
          pointsFontSize,
          progressBarHeight,
          progressBarRadius,
        ),
        // Podium rectangle
        Container(
          width: cardWidth,
          height: podiumHeight,
          decoration: BoxDecoration(
            color: podiumColor,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(cardWidth * 0.08),
              topRight: Radius.circular(cardWidth * 0.08),
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Badge image at top
              Padding(
                padding: EdgeInsets.only(top: cardWidth * 0.04),
                child: Image.asset(
                  _getPositionBadgeImage(position),
                  width: badgeSize,
                  height: badgeSize,
                  fit: BoxFit.contain,
                ),
              ),

              // JOIN button at bottom
              Padding(
                padding: EdgeInsets.only(
                  bottom: cardWidth * 0.04,
                  left: cardWidth * 0.04,
                  right: cardWidth * 0.04,
                ),
                child: SizedBox(
                  width: double.infinity,
                  height: joinBtnHeight,
                  child: ElevatedButton(
                    onPressed: onJoin,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.black87,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(cardWidth * 0.04),
                      ),
                      padding: EdgeInsets.symmetric(
                        vertical: joinBtnHeight * 0.1,
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'JOIN',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: joinFontSize,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPrizeCard(
    MegaPrize prize,
    BuildContext? context,
    double cardWidth,
    double cardImageHeight,
    double cardImageWidth,
    double badgeSize,
    double prizeFontSize,
    double pointsFontSize,
    double progressBarHeight,
    double progressBarRadius,
  ) {
    return Container(
      width: cardWidth,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(cardWidth * 0.12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            offset: Offset(0, cardWidth * 0.02),
            blurRadius: cardWidth * 0.08,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Product Image
          Padding(
            padding: EdgeInsets.all(cardWidth * 0.06),
            child: Container(
              height: cardImageHeight,
              width: cardImageWidth,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(cardWidth * 0.08),
                color: Colors.grey.shade100,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(cardWidth * 0.08),
                child: CachedNetworkImage(
                  imageUrl: prize.imageUrl,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey.shade200,
                    child: Icon(
                      Icons.image,
                      color: Colors.grey,
                      size: badgeSize * 0.7,
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey.shade200,
                    child: Icon(
                      Icons.error,
                      color: Colors.grey,
                      size: badgeSize * 0.7,
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Product Name
          Padding(
            padding: EdgeInsets.symmetric(horizontal: cardWidth * 0.06),
            child: Text(
              prize.name,
              style: TextStyle(
                fontSize: prizeFontSize,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Points
          Padding(
            padding: EdgeInsets.symmetric(vertical: cardWidth * 0.02),
            child: Text(
              '${prize.points} Pts',
              style: TextStyle(
                fontSize: pointsFontSize,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
          ),

          // Progress Bar
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: cardWidth * 0.08,
              vertical: cardWidth * 0.02,
            ),
            child: Column(
              children: [
                Container(
                  width: double.infinity,
                  height: progressBarHeight,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(progressBarRadius),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: _getProgressValue(prize.position),
                    child: Container(
                      decoration: BoxDecoration(
                        color: _getPositionColor(prize.position),
                        borderRadius: BorderRadius.circular(progressBarRadius),
                      ),
                    ),
                  ),
                ),
                SizedBox(height: cardWidth * 0.01),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getPositionColor(int position) {
    switch (position) {
      case 1:
        return const Color(0xFFFFBF00);
      case 2:
        return const Color(0xFFFFBF00);
      case 3:
        return const Color(0xFFFFBF00);
      default:
        return Colors.grey;
    }
  }

  IconData _getPositionIcon(int position) {
    switch (position) {
      case 1:
        return Icons.emoji_events;
      case 2:
        return Icons.military_tech;
      case 3:
        return Icons.workspace_premium;
      default:
        return Icons.star;
    }
  }

  String _getPositionText(int position) {
    switch (position) {
      case 1:
        return '1ST';
      case 2:
        return '2ND';
      case 3:
        return '3RD';
      default:
        return '${position}TH';
    }
  }

  String _getPositionBadgeImage(int position) {
    switch (position) {
      case 1:
        return 'assets/images/first.png';
      case 2:
        return 'assets/images/second.png';
      case 3:
        return 'assets/images/third.png';
      default:
        return 'assets/images/first.png';
    }
  }

  double _getProgressValue(int position) {
    switch (position) {
      case 1:
        return 0.85; // 85% progress for 1st place
      case 2:
        return 0.72; // 72% progress for 2nd place
      case 3:
        return 0.58; // 58% progress for 3rd place
      default:
        return 0.50; // 50% default progress
    }
  }
}
