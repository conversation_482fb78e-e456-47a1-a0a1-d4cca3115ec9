import 'package:flutter/material.dart';
import 'package:luckymall/modules/authorization/widgets/login_header.dart';
import 'package:luckymall/modules/authorization/widgets/phone_number_field.dart';
import 'package:luckymall/modules/authorization/widgets/verification_code_field.dart';
import 'package:luckymall/modules/authorization/widgets/password_field.dart';
import 'package:luckymall/modules/authorization/widgets/referral_code_field.dart';
import 'package:luckymall/modules/authorization/widgets/login_button.dart';

class RegisterView extends StatefulWidget {
  const RegisterView({Key? key}) : super(key: key);

  @override
  State<RegisterView> createState() => _RegisterViewState();
}

class _RegisterViewState extends State<RegisterView> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _verificationController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _referralController = TextEditingController();

  @override
  void dispose() {
    _phoneController.dispose();
    _verificationController.dispose();
    _passwordController.dispose();
    _referralController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFFFFFFF), Color(0xFFFCD255)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const LoginHeader(),
                const SizedBox(height: 10),
                // Phone Number Field
                PhoneNumberField(
                  controller: _phoneController,
                  hintText: 'Please enter you phone number',
                ),
                const SizedBox(height: 20),
                // Verification Code Field
                VerificationCodeField(
                  controller: _verificationController,
                  onSendCode: () {
                    // Handle send code functionality
                  },
                ),
                const SizedBox(height: 10),
                // Password Field
                PasswordField(
                  controller: _passwordController,
                  hintText: 'Please enter your password',
                ),
                const SizedBox(height: 10),
                // Referral Code Field
                ReferralCodeField(controller: _referralController),
                const SizedBox(height: 20),
                // Register Button
                LoginButton(
                  text: 'Register',
                  onPressed: () {
                    // Handle registration
                  },
                ),
                const SizedBox(height: 10),
                // Login Link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'Already have an account? ',
                      style: TextStyle(color: Colors.black, fontSize: 16),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: const Text(
                        'Log in',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
