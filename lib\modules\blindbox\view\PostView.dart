import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import '../view-model/PostVM.dart';

class PostView extends StatefulWidget {
  const PostView({super.key, required this.post});
  final Map<String, dynamic> post;

  @override
  State<PostView> createState() => _PostViewState();
}

class _PostViewState extends State<PostView> {
  late bool isFavorite;
  late int likeCount;

  @override
  void initState() {
    super.initState();
    isFavorite = widget.post['isFavorite'] ?? false;
    likeCount = widget.post['likeCount'] ?? 0;
  }

  void toggleFavorite() {
    setState(() {
      isFavorite = !isFavorite;
      if (isFavorite) {
        likeCount++;
      } else {
        likeCount = likeCount > 0 ? likeCount - 1 : 0;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final imageUrl = widget.post['imageUrl'] as String? ?? '';
    final username = widget.post['username'] as String? ?? 'Unknown';
    final title = widget.post['title'] as String? ?? '';

    final imageHeight = PostVM.getResponsiveImageHeight(context);
    final avatarRadius = PostVM.getResponsiveAvatarRadius(context);

    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(
          'Details',
          style: TextStyle(
            color: Colors.black,
            fontSize: PostVM.getResponsiveFontSize(18, context),
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            Icons.arrow_back_ios_new_rounded,
            color: Colors.black,
            size: PostVM.getResponsiveFontSize(24, context),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(top: BorderSide(color: Color(0xFFE8E8E8))),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User info row
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: PostVM.getResponsivePadding(16, context),
                vertical: PostVM.getResponsivePadding(12, context),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: avatarRadius,
                    backgroundColor: Colors.grey,
                    child: Icon(
                      Icons.person,
                      size: PostVM.getResponsiveFontSize(25, context),
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(width: PostVM.getResponsivePadding(10, context)),
                  Text(
                    username,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: PostVM.getResponsiveFontSize(16, context),
                    ),
                  ),
                ],
              ),
            ),
            // Main image with page indicator
            Stack(
              children: [
                SizedBox(
                  width: double.infinity,
                  height: imageHeight,
                  child: imageUrl.isNotEmpty
                      ? CachedNetworkImage(
                          imageUrl: imageUrl,
                          width: double.infinity,
                          height: imageHeight,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Center(
                            child: Shimmer.fromColors(
                              baseColor: Colors.grey[300]!,
                              highlightColor: Colors.grey[100]!,
                              child: Container(
                                width: double.infinity,
                                height: imageHeight,
                                color: Colors.grey[300]!,
                              ),
                            ),
                          ),
                          errorWidget: (context, url, error) => Center(
                            child: Icon(
                              Icons.broken_image,
                              size: PostVM.getResponsiveFontSize(60, context),
                              color: Colors.grey,
                            ),
                          ),
                        )
                      : Container(
                          width: double.infinity,
                          height: imageHeight,
                          color: Colors.grey[200],
                          child: Icon(
                            Icons.broken_image,
                            size: PostVM.getResponsiveFontSize(60, context),
                            color: Colors.grey,
                          ),
                        ),
                ),
                Positioned(
                  bottom: PostVM.getResponsivePadding(12, context),
                  left: 0,
                  right: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: PostVM.getResponsiveFontSize(8, context),
                        height: PostVM.getResponsiveFontSize(8, context),
                        decoration: BoxDecoration(
                          color: Colors.orange,
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: PostVM.getResponsivePadding(6, context)),
                      Container(
                        width: PostVM.getResponsiveFontSize(8, context),
                        height: PostVM.getResponsiveFontSize(8, context),
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          shape: BoxShape.circle,
                        ),
                      ),
                      SizedBox(width: PostVM.getResponsivePadding(6, context)),
                      Container(
                        width: PostVM.getResponsiveFontSize(8, context),
                        height: PostVM.getResponsiveFontSize(8, context),
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          shape: BoxShape.circle,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            // Details section at the bottom
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(bottom: BorderSide(color: Color(0xFFE8E8E8))),
                ),
                width: double.infinity,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: PostVM.getResponsivePadding(16, context),
                        vertical: PostVM.getResponsivePadding(8, context),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  title,
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: PostVM.getResponsiveFontSize(
                                      16,
                                      context,
                                    ),
                                  ),
                                ),
                                SizedBox(
                                  height: PostVM.getResponsivePadding(
                                    2,
                                    context,
                                  ),
                                ),
                                Text(
                                  '8 July 2025',
                                  style: TextStyle(
                                    color: Colors.black54,
                                    fontSize: PostVM.getResponsiveFontSize(
                                      13,
                                      context,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Row(
                            children: [
                              GestureDetector(
                                onTap: toggleFavorite,
                                child: Icon(
                                  isFavorite
                                      ? Icons.thumb_up
                                      : Icons.thumb_up_alt_outlined,
                                  color: isFavorite ? Colors.red : Colors.grey,
                                  size: PostVM.getResponsiveFontSize(
                                    20,
                                    context,
                                  ),
                                ),
                              ),
                              SizedBox(
                                width: PostVM.getResponsivePadding(4, context),
                              ),
                              Text(
                                likeCount.toString(),
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: PostVM.getResponsiveFontSize(
                                    15,
                                    context,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
