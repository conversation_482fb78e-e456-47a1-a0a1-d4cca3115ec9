import 'package:flutter/material.dart';
import 'package:luckymall/modules/authorization/widgets/login_header.dart';
import 'package:luckymall/modules/authorization/widgets/phone_number_field.dart';
import 'package:luckymall/modules/authorization/widgets/password_field.dart';
import 'package:luckymall/modules/authorization/widgets/login_actions_password.dart';
import 'package:luckymall/modules/authorization/widgets/login_button.dart';
import 'package:luckymall/modules/authorization/widgets/register_prompt.dart';
import 'package:luckymall/modules/authorization/view/ForgotPassword.dart';
import 'package:luckymall/modules/authorization/view/RegisterView.dart';

class PasswordLoginView extends StatefulWidget {
  const PasswordLoginView({Key? key}) : super(key: key);

  @override
  State<PasswordLoginView> createState() => _PasswordLoginViewState();
}

class _PasswordLoginViewState extends State<PasswordLoginView> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  void _handleLogin() {
    setState(() {
      _isLoading = true;
    });

    // Simulate login process
    Future.delayed(const Duration(seconds: 2), () {
      setState(() {
        _isLoading = false;
      });
      // Add your login logic here
    });
  }

  void _handleForgotPassword() {
    // Navigate to forgot password screen
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ForgotPasswordView()),
    );
  }

  void _handleRegister() {
    // Navigate to register screen
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const RegisterView()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFFFFFFF), Color(0xFFFCD255)],
          ),
        ),
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height,
            ),
            child: IntrinsicHeight(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const LoginHeader(),
                  PhoneNumberField(
                    controller: _phoneController,
                    onChanged: (value) {
                      // Handle phone number changes
                    },
                  ),
                  const SizedBox(height: 20), // Match OtpLoginView
                  PasswordField(
                    controller: _passwordController,
                    onChanged: (value) {
                      // Handle password changes
                    },
                  ),
                  const SizedBox(height: 12), // Match OtpLoginView
                  LoginActions(onForgotPassword: _handleForgotPassword),
                  const SizedBox(height: 24), // Match OtpLoginView
                  LoginButton(onPressed: _handleLogin, isLoading: _isLoading),
                  const SizedBox(height: 24), // Match OtpLoginView
                  RegisterPrompt(onRegisterTap: _handleRegister),
                  const SizedBox(height: 32), // Match OtpLoginView
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
