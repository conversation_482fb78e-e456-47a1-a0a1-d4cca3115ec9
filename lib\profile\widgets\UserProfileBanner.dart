import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class UserProfileBanner extends StatelessWidget {
  const UserProfileBanner({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 1),
      height: 130,
      decoration: BoxDecoration(
        color: const Color(0xFFD90019), // Red banner background
        borderRadius: BorderRadius.circular(5),
      ),
      child: Row(
        children: [
          // Left Side Text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  'Invite Friends,',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w900,
                    fontFamily: 'Solitreo', // Make sure this is declared in pubspec.yaml
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'There Are Rewards',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Solitreo',
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 12),
                const InviteNowButton()
              ],
            ),
          ),

          // Right Side SVG Image
          const SizedBox(width: 15),
          SvgPicture.asset(
            'assets/images/InviteFriendsBanner.svg',
            height: 130,
            width: 130,
            fit: BoxFit.contain,
          ),
        ],
      ),
    );
  }
}

class InviteNowButton extends StatelessWidget {
  const InviteNowButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFFFBF00),
        borderRadius: BorderRadius.circular(15),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text(
            'Invite Now!',
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(width: 8),
          SvgPicture.asset(
            'assets/icons/Click.svg', // Make sure this file exists in your assets
            height: 20,
            width: 20,
          ),
        ],
      ),
    );
  }
}
