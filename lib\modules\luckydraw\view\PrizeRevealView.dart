import 'package:flutter/material.dart';
import 'package:luckymall/modules/luckydraw/widgets/LuckyAppBar.dart';
import '../widgets/CategorySelectorWidget.dart';
import '../widgets/PointProductCard.dart';
import '../widgets/PointProductDetails.dart';
import '../widgets/SkeletonLoader.dart';
import '../widgets/MegaPrizes.dart';
import '../widgets/PrizeRevealCards.dart';
import 'ProductSearchPage.dart';
import '../widgets/OpenSoonCard.dart';

/// Prize Reveal View with conditional display logic
///
/// This view shows different content based on user's participation status:
/// - If user has participated in lucky draw and waiting for prize reveal: Shows Prize Reveal Cards first, then Mega Prizes
/// - If user hasn't participated: Shows only Mega Prizes at the top
///
/// To test the functionality:
/// 1. Initially, only Mega Prizes will be visible (hasParticipatedInLuckyDraw = false)
/// 2. Tap "Join" on any Mega Prize to simulate participation
/// 3. Prize Reveal Cards will appear above Mega Prizes
/// 4. The countdown timers will start automatically
class PrizeRevealView extends StatefulWidget {
  const PrizeRevealView({super.key});

  @override
  State<PrizeRevealView> createState() => _PrizeRevealViewState();
}

class _PrizeRevealViewState extends State<PrizeRevealView> {
  String selectedCategory = 'ALL';
  int minPointFilter = 0;
  int maxPointFilter = 1000;
  // Remove search-related state variables
  // String searchQuery = ''; // Add search query state
  // final TextEditingController _searchController = TextEditingController(); // Add search controller

  // Loading state for point products
  bool isPointProductsLoading = true;

  // Prize reveal state - whether user has participated and waiting for reveal
  bool hasParticipatedInLuckyDraw =
      false; // This should come from your backend/state management

  // Cache filtered products to avoid recalculation
  List<Map<String, dynamic>> _cachedFilteredProducts = [];
  String _lastFilterKey = '';

  @override
  void initState() {
    super.initState();
    _updateFilteredProducts();
    _simulateLoading();
    _checkUserParticipationStatus();
  }

  // Check if user has participated in lucky draw and waiting for prize reveal
  void _checkUserParticipationStatus() {
    // TODO: Replace this with actual API call to check user's participation status
    // For now, we'll simulate the check
    // In real implementation, this should check:
    // 1. If user has participated in any lucky draw
    // 2. If the lucky draw is completed but prize reveal countdown is still active
    // 3. If there are any pending prize reveals for the user

    // Simulate API call delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          // For demo purposes, you can change this to true to see the prize reveal cards
          hasParticipatedInLuckyDraw =
              false; // Set to true to show prize reveal cards
        });
      }
    });
  }

  // Method to simulate participating in a lucky draw (for testing)
  void _simulateParticipateInLuckyDraw() {
    setState(() {
      hasParticipatedInLuckyDraw = true;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Row(
          children: [
            Icon(Icons.celebration, color: Colors.white, size: 20),
            SizedBox(width: 8),
            Text(
              'Participated in lucky draw! Prize reveal countdown started.',
              style: TextStyle(fontSize: 9, fontWeight: FontWeight.w500),
            ),
          ],
        ),
        backgroundColor: const Color(0xFFFFBF00),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  // Remove dispose method since we're no longer using text controller
  // @override
  // void dispose() {
  //   _searchController.dispose(); // Dispose controller
  //   super.dispose();
  // }

  // Simulate loading
  void _simulateLoading() {
    Future.delayed(const Duration(milliseconds: 2000), () {
      if (mounted) {
        setState(() {
          isPointProductsLoading = false;
        });
      }
    });
  }

  // Update cached filtered products only when filters change
  void _updateFilteredProducts() {
    final filterKey = '$selectedCategory-$minPointFilter-$maxPointFilter';
    if (_lastFilterKey != filterKey) {
      _cachedFilteredProducts = allPointProducts.where((product) {
        final categoryMatch =
            selectedCategory == 'ALL' ||
            product['category'] == selectedCategory;
        final pointValue = product['pointValue'] as int;
        final pointMatch =
            pointValue >= minPointFilter && pointValue <= maxPointFilter;

        // Remove search functionality from here
        // final searchMatch = searchQuery.isEmpty ||
        //     product['productName'].toString().toLowerCase().contains(searchQuery.toLowerCase());

        return categoryMatch && pointMatch;
      }).toList();
      _lastFilterKey = filterKey;
    }
  }

  // Handle refresh functionality
  Future<void> _handleRefresh() async {
    setState(() {
      isPointProductsLoading = true;
    });

    _simulateLoading();

    await Future.delayed(const Duration(milliseconds: 1000));

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.refresh, color: Colors.white, size: 20),
              SizedBox(width: 8),
              Text(
                'Content refreshed successfully!',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
            ],
          ),
          backgroundColor: const Color(0xFFFFBF00),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  // Build Opening Soon Section
  Widget _buildOpeningSoonSection() {
    final List<OpenSoonProduct> openingSoonProducts = [
      const OpenSoonProduct(
        productName: 'Mouse Razer Pulsar X16 Gaming Mouse',
        imageUrl:
            'https://hnsgsfp.imgix.net/9/images/detailed/109/Razer_Cobra_Pro_Compact_Wireless_Gaming_Mouse_with_Underglow_Lighting_-_Black_(RZ01-04660100-R3)_01.JPG?fit=fill&bg=0FFF&w=1536&h=901&auto=format,compress',
        pointValue: 20000,
      ),
      const OpenSoonProduct(
        productName: 'Vacuum S8000',
        imageUrl:
            'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=400&fit=crop',
        pointValue: 20000,
      ),
      const OpenSoonProduct(
        productName: 'Mouse Razer X16 Gaming Mouse',
        imageUrl:
            'https://hnsgsfp.imgix.net/9/images/detailed/109/Razer_Cobra_Pro_Compact_Wireless_Gaming_Mouse_with_Underglow_Lighting_-_Black_(RZ01-04660100-R3)_01.JPG?fit=fill&bg=0FFF&w=1536&h=901&auto=format,compress',
        pointValue: 20000,
      ),
      const OpenSoonProduct(
        productName: 'Wireless Bluetooth Headphones',
        imageUrl:
            'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
        pointValue: 25000,
      ),
      const OpenSoonProduct(
        productName: 'Smartphone KingPinix 128GB',
        imageUrl:
            'https://image.made-in-china.com/365f3j00eQuCodqJMscr/3-Inch-IPS-Touch-Screen-2000mAh-Android-4G-LTE-Mini-Smartphone-4GB-RAM-128GB-ROM.webp',
        pointValue: 30000,
      ),
    ];

    return OpenSoonCard(
      products: openingSoonProducts,
      onProductTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.schedule, color: Colors.white, size: 20),
                SizedBox(width: 8),
                Text(
                  'This product will be available soon!',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
              ],
            ),
            backgroundColor: const Color(0xFFFFBF00),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      },
    );
  }

  // Build Prize Reveal Section
  Widget _buildPrizeRevealSection() {
    final List<PrizeRevealProduct> prizeRevealProducts = [
      const PrizeRevealProduct(
        productName: 'Mouse Razer Pulsar X16 Gaming Mouse',
        imageUrl:
            'https://hnsgsfp.imgix.net/9/images/detailed/109/Razer_Cobra_Pro_Compact_Wireless_Gaming_Mouse_with_Underglow_Lighting_-_Black_(RZ01-04660100-R3)_01.JPG?fit=fill&bg=0FFF&w=1536&h=901&auto=format,compress',
        countdownSeconds: 86399, // 23:59:59
      ),
      const PrizeRevealProduct(
        productName: 'Vacuum S8000',
        imageUrl:
            'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=400&fit=crop',
        countdownSeconds: 86399, // 23:59:59
      ),
      const PrizeRevealProduct(
        productName: 'Apuil Tag 1230',
        imageUrl:
            'https://hnsgsfp.imgix.net/9/images/detailed/109/Razer_Cobra_Pro_Compact_Wireless_Gaming_Mouse_with_Underglow_Lighting_-_Black_(RZ01-04660100-R3)_01.JPG?fit=fill&bg=0FFF&w=1536&h=901&auto=format,compress',
        countdownSeconds: 86399, // 23:59:59
      ),
    ];

    return PrizeRevealCards(
      products: prizeRevealProducts,
      onProductTap: () {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.timer, color: Colors.white, size: 20),
                SizedBox(width: 8),
                Text(
                  'Prize reveal countdown in progress!',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                ),
              ],
            ),
            backgroundColor: const Color(0xFFFFBF00),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      },
    );
  }

  // Sample point products data
  static const List<Map<String, dynamic>> allPointProducts = [
    {
      'productName':
          'Smarthpone KingPinix 128GB 4GB RAM 6.58inch 48MP Camera 6000mAh Battery',
      'imageUrl':
          'https://image.made-in-china.com/365f3j00eQuCodqJMscr/3-Inch-IPS-Touch-Screen-2000mAh-Android-4G-LTE-Mini-Smartphone-4GB-RAM-128GB-ROM.webp',
      'variation': 'Large Size',
      'pointValue': 50,
      'rating': 4.6,
      'claimsRemaining': 15,
      'currentParticipants': 35,
      'maxParticipants': 50,
      'category': 'Hot Goods',
      'description':
          'Experience culinary excellence with the Tefal & Jamie Oliver Cook\'s Classic All-In-One Pot. This versatile cooking vessel combines innovative design with practical functionality, perfect for creating delicious meals with ease. Featuring premium non-stick coating, heat-resistant handles, and optimal heat distribution for consistent cooking results.',
      'reviews': [
        {
          'reviewerName': 'Sarah Johnson',
          'avatarUrl': 'https://randomuser.me/api/portraits/women/68.jpg',
          'date': '2025 / 01 / 15',
          'rating': 5,
          'variation': 'Large Size',
          'reviewText':
              'Amazing quality! This pot has transformed my cooking experience. The non-stick surface works perfectly and it\'s so easy to clean.',
          'imageUrls': [
            'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop',
          ],
        },
        {
          'reviewerName': 'Michael Chen',
          'avatarUrl': 'https://randomuser.me/api/portraits/men/32.jpg',
          'date': '2025 / 01 / 10',
          'rating': 4,
          'variation': 'Medium Size',
          'reviewText':
              'Great value for money. Perfect size for my family and the heat distribution is excellent.',
          'imageUrls': [
            'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop',
          ],
        },
      ],
    },
    {
      'productName': 'Gaming Mouse Razer DeathAdder V3',
      'imageUrl':
          'https://hnsgsfp.imgix.net/9/images/detailed/109/Razer_Cobra_Pro_Compact_Wireless_Gaming_Mouse_with_Underglow_Lighting_-_Black_(RZ01-04660100-R3)_01.JPG?fit=fill&bg=0FFF&w=1536&h=901&auto=format,compress',
      'pointValue': 50,
      'rating': 4.8,
      'claimsRemaining': 8,
      'currentParticipants': 22,
      'maxParticipants': 30,
      'category': 'Hot Goods',
      'description':
          'Elevate your gaming experience with the Razer DeathAdder V3 Gaming Mouse. Featuring precision-engineered sensor technology with ultra-accurate tracking up to 30,000 DPI, ergonomic design for extended gaming sessions, and customizable RGB lighting. Built for professional gamers and enthusiasts who demand the best performance.',
      'reviews': [
        {
          'reviewerName': 'Alex Rivera',
          'avatarUrl': 'https://randomuser.me/api/portraits/men/15.jpg',
          'date': '2025 / 01 / 20',
          'rating': 5,
          'variation': 'Black',
          'reviewText':
              'This mouse is incredible! The precision and responsiveness are outstanding. Perfect for competitive gaming.',
          'imageUrls': [
            'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
          ],
        },
        {
          'reviewerName': 'Jessica Wong',
          'avatarUrl': 'https://randomuser.me/api/portraits/women/28.jpg',
          'date': '2025 / 01 / 18',
          'rating': 5,
          'variation': 'Black',
          'reviewText':
              'Best gaming mouse I\'ve ever used. The ergonomics are perfect and the sensor is extremely accurate.',
          'imageUrls': [
            'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
          ],
        },
      ],
    },
    {
      'productName': 'TNG eWallet RM10 Voucher',
      'imageUrl':
          'https://www.barbecook.com/cdn/shop/articles/BC-WOO-6006-SF-03-HR_4a57e31f-cea3-450e-8ac6-39ea475821f0.jpg?v=1678958409',
      'pointValue': 100,
      'rating': 4.9,
      'claimsRemaining': 50,
      'currentParticipants': 8,
      'maxParticipants': 25,
      'category': 'TNG Vouchers',
      'description':
          'Get instant value with this TNG eWallet RM10 Voucher. Perfect for everyday purchases, online shopping, or dining out. Easy to redeem and can be used at thousands of merchants nationwide. No expiry date and instant activation upon redemption.',
      'reviews': [
        {
          'reviewerName': 'David Tan',
          'avatarUrl': 'https://randomuser.me/api/portraits/men/45.jpg',
          'date': '2025 / 01 / 25',
          'rating': 5,
          'variation': 'Digital Voucher',
          'reviewText':
              'Super convenient! Received the voucher instantly and used it immediately. Great value!',
          'imageUrls': [],
        },
        {
          'reviewerName': 'Lisa Kumar',
          'avatarUrl': 'https://randomuser.me/api/portraits/women/38.jpg',
          'date': '2025 / 01 / 22',
          'rating': 5,
          'variation': 'Digital Voucher',
          'reviewText':
              'Perfect for small purchases. The redemption process is very smooth and quick.',
          'imageUrls': [],
        },
      ],
    },
    {
      'productName': 'TNG eWallet RM20 Voucher',
      'imageUrl':
          'https://images.getbats.com/item/ecget/20230613_9e4c1c2b6aff4a43f3c00c8619e7c6ef.png',
      'pointValue': 180,
      'rating': 4.9,
      'claimsRemaining': 25,
      'currentParticipants': 18,
      'maxParticipants': 20,
      'category': 'TNG Vouchers',
      'description':
          'Double the value with this TNG eWallet RM20 Voucher. Ideal for larger purchases, meal deliveries, or shopping sprees. Accepted at all TNG partner merchants including restaurants, retail stores, and online platforms. Easy redemption with instant activation.',
      'reviews': [
        {
          'reviewerName': 'Ahmad Hassan',
          'avatarUrl': 'https://randomuser.me/api/portraits/men/22.jpg',
          'date': '2025 / 01 / 23',
          'rating': 5,
          'variation': 'Digital Voucher',
          'reviewText':
              'Excellent value! Used it for my lunch orders and it worked perfectly. Will definitely get more.',
          'imageUrls': [],
        },
      ],
    },
    {
      'productName': 'Wireless Bluetooth Headphones',
      'imageUrl':
          'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
      'pointValue': 75,
      'rating': 4.5,
      'claimsRemaining': 12,
      'currentParticipants': 15,
      'maxParticipants': 40,
      'category': 'Hot Goods',
      'description':
          'Immerse yourself in superior sound quality with these Wireless Bluetooth Headphones. Featuring active noise cancellation, 30-hour battery life, and premium audio drivers for crystal-clear sound. Perfect for music lovers, travelers, and professionals who demand excellent audio performance.',
      'reviews': [
        {
          'reviewerName': 'Emily Parker',
          'avatarUrl': 'https://randomuser.me/api/portraits/women/55.jpg',
          'date': '2025 / 01 / 19',
          'rating': 4,
          'variation': 'Black',
          'reviewText':
              'Great sound quality and very comfortable to wear. The battery life is impressive, lasting almost the full day.',
          'imageUrls': [
            'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
          ],
        },
        {
          'reviewerName': 'Robert Kim',
          'avatarUrl': 'https://randomuser.me/api/portraits/men/67.jpg',
          'date': '2025 / 01 / 16',
          'rating': 5,
          'variation': 'Black',
          'reviewText':
              'Excellent noise cancellation! Perfect for my daily commute and work from home sessions.',
          'imageUrls': [
            'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
          ],
        },
      ],
    },
    {
      'productName': 'TNG eWallet RM50 Voucher',
      'imageUrl':
          'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=400&fit=crop',
      'pointValue': 450,
      'rating': 5.0,
      'claimsRemaining': 5,
      'currentParticipants': 45,
      'maxParticipants': 50,
      'category': 'TNG Vouchers',
      'description':
          'Maximum value with this TNG eWallet RM50 Voucher. Perfect for big purchases, family dining, or special occasions. Accepted at premium restaurants, department stores, and luxury retailers. Premium voucher with instant activation and no restrictions on usage.',
      'reviews': [
        {
          'reviewerName': 'Maria Santos',
          'avatarUrl': 'https://randomuser.me/api/portraits/women/42.jpg',
          'date': '2025 / 01 / 24',
          'rating': 5,
          'variation': 'Digital Voucher',
          'reviewText':
              'Amazing value! Used it for a family dinner and still had some left over. Highly recommend!',
          'imageUrls': [],
        },
        {
          'reviewerName': 'James Wilson',
          'avatarUrl': 'https://randomuser.me/api/portraits/men/38.jpg',
          'date': '2025 / 01 / 21',
          'rating': 5,
          'variation': 'Digital Voucher',
          'reviewText':
              'Best voucher deal ever! The redemption was instant and I could use it immediately for online shopping.',
          'imageUrls': [],
        },
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: LuckyAppBar(),
      backgroundColor: Colors.white,
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        color: const Color(0xFFFFBF00),
        backgroundColor: Colors.white,
        strokeWidth: 3.0,
        displacement: 50.0,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Search Bar and gradient area
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xFFFFD54F), Color(0xFFFFFFFF)],
                  ),
                ),
                child: Column(
                  children: [
                    // Search Bar at the very top - matching shop UI style
                    Container(
                      padding: const EdgeInsets.fromLTRB(15, 0, 15, 0),
                      child: GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => ProductSearchPage(
                                allProducts: allPointProducts,
                              ),
                            ),
                          );
                        },
                        child: Container(
                          height: 36,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Padding(
                                padding: EdgeInsets.symmetric(horizontal: 12),
                                child: Icon(
                                  Icons.search,
                                  color: Colors.grey,
                                  size: 20,
                                ),
                              ),
                              const Expanded(
                                child: Text(
                                  'Search Products',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              Container(
                                height: 32,
                                margin: const EdgeInsets.symmetric(
                                  vertical: 2,
                                  horizontal: 4,
                                ),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 18,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.red[700],
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Center(
                                  child: Text(
                                    'Search',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 30),
                  ],
                ),
              ),

              // Conditional Prize Reveal Section - only show if user has participated
              if (hasParticipatedInLuckyDraw) ...[
                _buildPrizeRevealSection(),
                const SizedBox(height: 10),
              ],

              // Mega Prizes Widget - always show, but position depends on prize reveal visibility
              MegaPrizes(
                prizes: [], // Using default prizes
                onPrizeJoin: () {
                  // Handle prize join action and simulate participation
                  _simulateParticipateInLuckyDraw();
                },
              ),
              const SizedBox(height: 12),

              // Opening Soon Section
              _buildOpeningSoonSection(),

              const SizedBox(height: 20),

              const SizedBox(height: 20),

              // Divider
              Divider(
                color: Color(0xFFffbf00),
                thickness: 3,
                indent: 20,
                endIndent: 20,
              ),
              const SizedBox(height: 30),

              Center(
                child: Text(
                  'Point Products',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),

              const SizedBox(height: 20),

              // Category Selector Widget
              CategorySelectorWidget(
                onCategoryChanged: (category) {
                  setState(() {
                    selectedCategory = category;
                    _updateFilteredProducts();
                  });
                  print('Selected category: $category');
                },
                onPointRangeChanged: (min, max) {
                  setState(() {
                    minPointFilter = min;
                    maxPointFilter = max;
                    _updateFilteredProducts();
                  });
                  print('Point range changed: $min - $max');
                },
              ),

              const SizedBox(height: 16),

              // Filter Results Info
              if (!isPointProductsLoading)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          'Showing ${_cachedFilteredProducts.length} product${_cachedFilteredProducts.length != 1 ? 's' : ''}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black54,
                          ),
                        ),
                      ),
                      if (selectedCategory != 'ALL' ||
                          minPointFilter != 0 ||
                          maxPointFilter != 1000)
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              selectedCategory = 'ALL';
                              minPointFilter = 0;
                              maxPointFilter = 1000;
                              _updateFilteredProducts();
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFFFFBF00).withOpacity(0.2),
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: const Color(0xFFFFBF00),
                              ),
                            ),
                            child: const Text(
                              'Clear Filters',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

              const SizedBox(height: 16),

              // Point Products Grid
              isPointProductsLoading
                  ? const PointProductGridSkeleton()
                  : PointProductGrid(
                      products: _cachedFilteredProducts,
                      onProductTap: (product) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                PointProductDetails(product: product),
                          ),
                        );
                      },
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
