import 'package:flutter/material.dart';
import '../../model/cardPack/CardItem.dart';

class CardGridDisplay extends StatelessWidget {
  final List<CardItem> cards;
  final String category;
  final bool showArchived;

  const CardGridDisplay({
    super.key,
    required this.cards,
    required this.category,
    required this.showArchived,
  });

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double cardWidth = (screenWidth - 48) / 2;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: GridView.builder(
        itemCount: cards.length,
        padding: const EdgeInsets.only(bottom: 20),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 12,
          childAspectRatio: 3 / 4,
        ),
        itemBuilder: (context, index) {
          final CardItem card = cards[index];

          return GestureDetector(
            onTap: () {
              // TODO: handle card tap
            },
            child: Stack(
              children: [
                // Card Image with optional dimming for locked
                Container(
                  width: cardWidth,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: const [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 8,
                        offset: Offset(0, 2),
                      ),
                    ],
                    image: DecorationImage(
                      image: AssetImage(card.imagePath),
                      fit: BoxFit.cover,
                      colorFilter: card.isOwned
                          ? null
                          : ColorFilter.mode(Colors.black.withOpacity(0.6), BlendMode.darken),
                    ),
                  ),
                ),

                // Lock Icon for unowned cards
                if (!card.isOwned)
                  const Positioned.fill(
                    child: Center(
                      child: Icon(Icons.lock, color: Colors.white70, size: 30),
                    ),
                  ),

                // Card Info: name + category
                Positioned(
                  bottom: 12,
                  left: 12,
                  right: 12,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        card.name,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        card.category.toUpperCase(),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),

                // Quantity Badge (bottom-right)
                if (card.isOwned)
                  Positioned(
                    bottom: 5,
                    right: 5,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: screenWidth * 0.035, 
                        vertical: screenWidth * 0.015,
                      ),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFFFFD54F), Color(0xFFFFB300)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          bottomRight: Radius.circular(6)
                          
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color.fromARGB(33, 255, 193, 7),
                            blurRadius: 6,
                            offset: const Offset(1, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        '${card.quantity}',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: screenWidth * 0.03,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.3,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}
