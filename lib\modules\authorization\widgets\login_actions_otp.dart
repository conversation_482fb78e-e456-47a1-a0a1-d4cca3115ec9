import 'package:flutter/material.dart';
import 'package:luckymall/modules/authorization/view/PasswordLoginView.dart';

class LoginActions extends StatelessWidget {
  final VoidCallback? onForgotPassword;
  final VoidCallback? onOtpLogin;

  const LoginActions({Key? key, this.onForgotPassword, this.onOtpLogin})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          GestureDetector(
            onTap:
                onOtpLogin ??
                () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PasswordLoginView(),
                    ),
                  );
                },
            child: const Text(
              'Password Login',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
