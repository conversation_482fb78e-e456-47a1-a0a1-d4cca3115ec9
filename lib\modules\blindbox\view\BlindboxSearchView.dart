import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-model/BlindboxSearchVM.dart';
import '../widgets/SearchFilterTabs.dart';
import '../widgets/BlindboxShopCard.dart';
import 'BlindboxBuyView.dart';

class BlindboxSearchView extends StatefulWidget {
  final String? initialSearchQuery;

  const BlindboxSearchView({super.key, this.initialSearchQuery});

  @override
  State<BlindboxSearchView> createState() => _BlindboxSearchViewState();
}

class _BlindboxSearchViewState extends State<BlindboxSearchView> {
  late TextEditingController _searchController;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(
      text: widget.initialSearchQuery ?? '',
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  // Helper method to get responsive crossAxisCount for grid
  int getResponsiveCrossAxisCount(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 2; // Small devices (phones)
    } else if (screenWidth < 900) {
      return 3; // Medium devices (tablets)
    } else if (screenWidth < 1200) {
      return 4; // Large tablets
    } else {
      return 5; // Desktop/large screens
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => BlindboxSearchVM(initialQuery: widget.initialSearchQuery),
      child: Consumer<BlindboxSearchVM>(
        builder: (context, vm, _) {
          return Scaffold(
            backgroundColor: const Color(0xFFF1F1F1),
            appBar: AppBar(
              backgroundColor: Colors.white,
              elevation: 0,
              toolbarHeight: 0,
            ),
            body: Column(
              children: [
                // Top section with search bar
                Container(
                  decoration: const BoxDecoration(
                    color: Color(0xFFFFFFFF), // Light gray background
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: EdgeInsets.all(
                        getResponsivePadding(16, context),
                      ),
                      child: Row(
                        children: [
                          // Back button
                          GestureDetector(
                            onTap: () => Navigator.of(context).pop(),
                            child: Icon(
                              Icons.arrow_back_ios,
                              size: getResponsiveFontSize(20, context),
                              color: Colors.black,
                            ),
                          ),
                          SizedBox(width: getResponsivePadding(8, context)),

                          // Search bar
                          Expanded(
                            child: Container(
                              height: getResponsivePadding(40, context),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF1F1F1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  Padding(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: getResponsivePadding(
                                        12,
                                        context,
                                      ),
                                    ),
                                    child: Icon(
                                      Icons.search,
                                      color: Colors.black,
                                      size: getResponsiveFontSize(20, context),
                                    ),
                                  ),
                                  Expanded(
                                    child: TextField(
                                      controller: vm.searchController,
                                      decoration: InputDecoration(
                                        hintText: 'Search',
                                        border: InputBorder.none,
                                        isDense: true,
                                        contentPadding: EdgeInsets.symmetric(
                                          vertical: getResponsivePadding(
                                            10,
                                            context,
                                          ),
                                        ),
                                        hintStyle: TextStyle(
                                          color: Colors.grey[500],
                                          fontSize: getResponsiveFontSize(
                                            14,
                                            context,
                                          ),
                                        ),
                                      ),
                                      style: TextStyle(
                                        fontSize: getResponsiveFontSize(
                                          14,
                                          context,
                                        ),
                                      ),
                                    ),
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(
                                      right: getResponsivePadding(4, context),
                                    ),
                                    child: Material(
                                      color: const Color(0xFFFCD255),
                                      borderRadius: BorderRadius.circular(6),
                                      child: InkWell(
                                        borderRadius: BorderRadius.circular(6),
                                        onTap: () {
                                          vm.performSearch();
                                        },
                                        child: Padding(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: getResponsivePadding(
                                              16,
                                              context,
                                            ),
                                            vertical: getResponsivePadding(
                                              8,
                                              context,
                                            ),
                                          ),
                                          child: Text(
                                            'Search',
                                            style: TextStyle(
                                              color: Colors.black,
                                              fontWeight: FontWeight.w600,
                                              fontSize: getResponsiveFontSize(
                                                12,
                                                context,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(height: getResponsivePadding(8, context)),
                // Filter tabs
                SearchFilterTabs(
                  selectedFilter: vm.selectedFilter,
                  onFilterSelected: vm.selectFilter,
                ),

                // Divider
                Container(height: 1, color: Colors.grey[300]),

                // Search results
                Expanded(
                  child: vm.isLoading
                      ? _buildLoadingState(context)
                      : vm.searchResults.isEmpty
                      ? _buildEmptyState(context)
                      : _buildSearchResults(context, vm),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(getResponsivePadding(32, context)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: getResponsiveFontSize(80, context),
              color: Colors.grey[400],
            ),
            SizedBox(height: getResponsivePadding(16, context)),
            Text(
              'No results found',
              style: TextStyle(
                fontSize: getResponsiveFontSize(18, context),
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            SizedBox(height: getResponsivePadding(8, context)),
            Text(
              'Try searching for something else',
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults(BuildContext context, BlindboxSearchVM vm) {
    final crossAxisCount = getResponsiveCrossAxisCount(context);

    return Padding(
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          mainAxisSpacing: getResponsivePadding(16, context),
          crossAxisSpacing: getResponsivePadding(16, context),
          childAspectRatio: 0.8,
        ),
        itemCount: vm.searchResults.length,
        itemBuilder: (context, index) {
          final product = vm.searchResults[index];

          final isGroupBuy = product['isGroupBuy'] == true;

          return BlindboxShopCard(
            title: product['title'] as String,
            price: product['price'] as String,
            rating: product['rating'] as double,
            soldCount: product['soldCount'] as int,
            maxSavings: product['maxSavings'] as String?,
            imageUrl: product['imageUrl'] as String?,
            isGroupBuy: isGroupBuy,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      BlindboxBuyView(isGroupBuy: isGroupBuy, product: product),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    final crossAxisCount = getResponsiveCrossAxisCount(context);

    return Padding(
      padding: EdgeInsets.all(getResponsivePadding(16, context)),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          mainAxisSpacing: getResponsivePadding(16, context),
          crossAxisSpacing: getResponsivePadding(16, context),
          childAspectRatio: 0.8,
        ),
        itemCount: 6, // Show 6 shimmer cards
        itemBuilder: (context, index) => const BlindboxShopCardShimmer(),
      ),
    );
  }
}
