import 'package:flutter/material.dart';

class LuckyAppBar extends StatelessWidget implements PreferredSizeWidget {
  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375;
    double scaleH(double value) => value * screenHeight / 812;
    double scaleText(double value) => value * screenWidth / 375;

    return AppBar(
      backgroundColor: const Color(0xFFFFD54F), // yellow background
      elevation: 0,
      titleSpacing: 0,
      title: Row(
        children: [
          SizedBox(width: scaleW(12)),
          // App Logo
          CircleAvatar(
            backgroundColor: Colors.white,
            radius: scaleW(18),
            backgroundImage: const AssetImage(
              'assets/images/logoLM.png',
            ), // Replace with your asset path
          ),
          SizedBox(width: scaleW(10)),
          Text(
            'MY Lucky Mall',
            style: TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
              fontSize: scaleText(16),
            ),
          ),
          const Spacer(),
          // Book Icon
          IconButton(
            icon: Icon(Icons.menu_book, color: Colors.black, size: scaleW(24)),
            onPressed: () {
              // TODO: Navigate to guide/book page
            },
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(
              minWidth: scaleW(40),
              minHeight: scaleH(40),
            ),
          ),
          // Cart Icon
          IconButton(
            icon: Icon(
              Icons.shopping_cart,
              color: Colors.black,
              size: scaleW(24),
            ),
            onPressed: () {
              // TODO: Navigate to cart page
            },
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(
              minWidth: scaleW(40),
              minHeight: scaleH(40),
            ),
          ),
        ],
      ),
    );
  }
}
