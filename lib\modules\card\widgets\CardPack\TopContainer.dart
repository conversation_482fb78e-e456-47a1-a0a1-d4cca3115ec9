import 'package:flutter/material.dart';
import 'package:flutter_switch/flutter_switch.dart';

class TopContainer extends StatelessWidget {
  final VoidCallback onHistoryTap;
  final VoidCallback onPhysicalCardsTap;
  final VoidCallback onSelectCategory;
  final int cardCount;
  final bool showArchived;
  final ValueChanged<bool> onToggleArchived;

  const TopContainer({
    super.key,
    required this.onHistoryTap,
    required this.onPhysicalCardsTap,
    required this.onSelectCategory,
    required this.cardCount,
    required this.showArchived,
    required this.onToggleArchived,
  });

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double screenHeight = MediaQuery.of(context).size.height;
    final double containerHeight = screenHeight < 700 ? 140 : 160;
    final double bottomBarHeight = containerHeight * 0.38; // Responsive: ~38% of top container

    return Column(
      children: [
        const SizedBox(height: 12),
        SizedBox(
          height: containerHeight,
          child: Stack(
            children: [
              // Yellow Top Container
              Container(
                height: containerHeight,
                margin: const EdgeInsets.only(left: 12, right: 12, bottom: 0),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color.fromRGBO(255, 191, 0, 1), Color.fromRGBO(244, 210, 109, 1)],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Color.fromARGB(66, 0, 0, 0),
                      blurRadius: 12,
                      offset: const Offset(2, 4),
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.only(
                    left: 16,
                    right: 0,
                    top: screenHeight < 700 ? 10 : 16,
                    bottom: bottomBarHeight, // Reserve space for bottom bar
                  ),
                  child: Row(
                    children: [
                      // History Button
                      Expanded(
                        child: SizedBox(
                          height: screenHeight < 700 ? 44 : 48,
                          child: _GameTabButton(
                            icon: Icons.history,
                            label: "History",
                            onTap: onHistoryTap,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(10),
                              bottomRight: Radius.circular(10),
                              topRight: Radius.circular(5),
                              bottomLeft: Radius.circular(5),
                            ),
                            fontSize: screenWidth < 350 ? 12 : 13,
                            iconSize: screenWidth < 350 ? 24 : 28,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Physical Cards Button
                      Expanded(
                        child: SizedBox(
                          height: screenHeight < 700 ? 44 : 48,
                          child: _GameTabButton(
                            icon: Icons.credit_card,
                            label: "Physical Card",
                            onTap: onPhysicalCardsTap,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(10),
                              bottomRight: Radius.circular(10),
                              topRight: Radius.circular(5),
                              bottomLeft: Radius.circular(5),
                            ),
                            fontSize: screenWidth < 350 ? 12 : 13,
                            iconSize: screenWidth < 350 ? 24 : 28,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Select Category Button (flush right)
                      SizedBox(
                        height: screenHeight < 700 ? 44 : 48,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: Colors.black87,
                            shape: const RoundedRectangleBorder(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(28),
                                bottomLeft: Radius.circular(28),
                                topRight: Radius.circular(0),
                                bottomRight: Radius.circular(0),
                              ),
                            ),
                            elevation: 2,
                            padding: EdgeInsets.symmetric(
                              horizontal: screenWidth < 350 ? 12 : 20,
                              vertical: 0,
                            ),
                          ),
                          onPressed: onSelectCategory,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: const [
                              Flexible(
                                child: Text(
                                  "Select Category",
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                  textAlign: TextAlign.center,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              SizedBox(width: 8),
                              Icon(Icons.play_arrow_sharp, size: 20),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Bottom grey container (Collect Cards & Archived)
              Positioned(
                left: 12,
                right: 12,
                bottom: 0,
                child: Container(
                  height: bottomBarHeight,
                  decoration: const BoxDecoration(
                    color: Color(0xFFD9D9D9),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Color.fromARGB(31, 0, 0, 0),
                        blurRadius: 8,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: screenWidth < 350 ? 8 : 16,
                    vertical: 8,
                  ),
                  child: Row(
                    children: [
                      Text(
                        'Collect Cards',
                        style: TextStyle(
                          fontSize: screenWidth < 350 ? 13 : 15,
                          color: Colors.black87,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Inner shadow effect for card count
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Color.fromARGB(25, 0, 0, 0), // 0.1 * 255 ≈ 25
                                  blurRadius: 1,
                                  offset: const Offset(2, 2),
                                ),
                              ],
                            ),
                            padding: EdgeInsets.symmetric(
                              horizontal: screenWidth < 350 ? 10 : 14,
                              vertical: 6,
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.diamond, color: Colors.grey, size: screenWidth < 350 ? 16 : 18),
                                const SizedBox(width: 4),
                                Text(
                                  '$cardCount',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: screenWidth < 350 ? 13 : 15,
                                    color: Colors.black87,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Fake inner shadow
                          Positioned.fill(
                            child: IgnorePointer(
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Color.fromARGB(20, 0, 0, 0), // 0.08 * 255 ≈ 20
                                      blurRadius: 8,
                                      spreadRadius: -6,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const Spacer(),
                      Icon(Icons.archive, color: Colors.black54, size: screenWidth < 350 ? 18 : 22),
                      const SizedBox(width: 4),
                      Text(
                        'Archived',
                        style: TextStyle(
                          fontSize: screenWidth < 350 ? 13 : 15,
                          color: Colors.black87,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 4),
                      // Custom toggle switch
                      Container(
                        width: screenWidth < 350 ? 48 : 60,
                        height: screenWidth < 350 ? 26 : 32,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Color.fromARGB(25, 0, 0, 0), // 0.1 * 255 ≈ 25
                              blurRadius: 2,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        alignment: Alignment.center,
                        child: FlutterSwitch(
                          value: showArchived,
                          onToggle: onToggleArchived,
                          width: screenWidth < 350 ? 48 : 60,
                          height: screenWidth < 350 ? 26 : 32,
                          toggleSize: screenWidth < 350 ? 26 : 32,
                          activeColor: Colors.amber,
                          inactiveColor: Colors.grey.shade600,
                          toggleColor: Colors.white,
                          padding: 0,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _GameTabButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onTap;
  final BorderRadius borderRadius;
  final double fontSize;
  final double iconSize;

  const _GameTabButton({
    required this.icon,
    required this.label,
    required this.onTap,
    required this.borderRadius,
    required this.fontSize,
    required this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white,
      borderRadius: borderRadius,
      elevation: 2,
      child: InkWell(
        borderRadius: borderRadius,
        onTap: onTap,
        child: Container(
          alignment: Alignment.center,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: Colors.grey[700], size: iconSize),
              const SizedBox(height: 2),
              Flexible(
                child: Text(
                  label,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.grey[800],
                    fontWeight: FontWeight.normal,
                    fontSize: fontSize,
                  ),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}