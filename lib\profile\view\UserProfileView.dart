import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class UserProfileView extends StatelessWidget {
  const UserProfileView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Stack(
          children: [
            // Yellow background - responsive height
            Container(
              width: double.infinity,
              height: _getHeaderHeight(context),
              color: const Color(0xFFFFEAAB),
            ),

            // Top-right icons
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              right: 16,
              child: Row(
                children: [
                  _TopIcon('assets/icons/Customer Support.svg'),
                  _TopIcon('assets/icons/Notification.svg'),
                  _TopIcon('assets/icons/Settings 02.svg'),
                ],
              ),
            ),

            // Main content
            Column(
              children: [
                SizedBox(height: MediaQuery.of(context).padding.top + 40),
                
                // Profile section
                ProfileSection(
                  username: 'Angela',
                  points: '1020',
                  cardCount: '10',
                ),

                // Action buttons and info cards
                _buildActionContainer(context),
                
                const SizedBox(height: 16),
                
                // Updated invite friends banner
                Padding(
                  padding: _getHorizontalPadding(context),
                  child: const UserProfileBanner(),
                ),
                
                const SizedBox(height: 24),
                
                // Feature grid
                _buildFeatureGrid(context),
                
                const SizedBox(height: 32),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Get responsive header height
  double _getHeaderHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 600) { // Tablet
      return 380;
    }
    return 346; // Phone
  }

  // Get responsive horizontal padding
  EdgeInsets _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 600) { // Tablet
      return EdgeInsets.symmetric(horizontal: screenWidth * 0.1);
    }
    return const EdgeInsets.symmetric(horizontal: 16.0);
  }

  // Get responsive grid columns
  int _getGridColumns(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 900) return 6; // Large tablet/desktop
    if (screenWidth > 600) return 5; // Tablet
    return 4; // Phone
  }

  // Extracted method for action container
  Widget _buildActionContainer(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: _getHorizontalPadding(context).horizontal / 2, 
        vertical: 12
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.shade400.withOpacity(0.9),
              blurRadius: 12,
              spreadRadius: 1,
              offset: const Offset(0, 6),
            )
          ],
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: const [
                _ActionIcon(
                  title: 'My Orders',
                  imagePath: 'assets/icons/My Order.svg',
                ),
                _ActionIcon(
                  title: 'Check-in',
                  imagePath: 'assets/icons/Check In.svg',
                ),
                _ActionIcon(
                  title: 'Exchange',
                  imagePath: 'assets/icons/Exchange.svg',
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _InfoCard(
                    title: 'Balance',
                    value: 'RM300',
                    imagePath: 'assets/icons/Balance.svg',
                  ),
                ),
                Container(
                  width: 1,
                  height: 48,
                  color: Colors.grey.shade300,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                ),
                Expanded(
                  child: _InfoCard(
                    title: 'Friends invited',
                    value: '42069',
                    imagePath: 'assets/icons/Invite Friends.svg',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Extracted method for responsive feature grid
  Widget _buildFeatureGrid(BuildContext context) {
    const features = [
      'Tutorial', 'My Favourite', 'Voucher', 'Lucky Draw Record',
      'My Sharing', 'My Review', 'My Address', 'Lucky Group Record',
      'Face Verification'
    ];
    
    const featureIcons = [
      'assets/icons/Tutorial.svg', 
      'assets/icons/My Favorite.svg', 
      'assets/icons/Voucher.svg', 
      'assets/icons/Participant Record.svg',
      'assets/icons/My Sharing.svg', 
      'assets/icons/My Review.svg', 
      'assets/icons/Address.svg', 
      'assets/icons/Lucky Draw Group.svg',
      'assets/icons/Face Verification.svg'
    ];

    final screenWidth = MediaQuery.of(context).size.width;
    final crossAxisCount = _getGridColumns(context);
    final childAspectRatio = screenWidth > 600 ? 0.9 : 0.8;

    return Padding(
      padding: _getHorizontalPadding(context),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: crossAxisCount,
          childAspectRatio: childAspectRatio,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: features.length,
        itemBuilder: (context, index) {
          return _FeatureItem(
            imagePath: featureIcons[index],
            label: features[index],
          );
        },
      ),
    );
  }

  // Helper widget for top icons - responsive sizing
  Widget _TopIcon(String assetPath) {
    return Builder(
      builder: (context) {
        final screenWidth = MediaQuery.of(context).size.width;
        final iconSize = screenWidth > 600 ? 32.0 : 28.0;
        
        return Padding(
          padding: const EdgeInsets.only(left: 8),
          child: SvgPicture.asset(
            assetPath,
            width: iconSize,
            height: iconSize,
            color: Colors.black,
          ),
        );
      }
    );
  }
}

// Updated Gradient Icon Widget with responsive sizing
class _GradientIcon extends StatelessWidget {
  final String assetPath;
  final double width;
  final double height;
  final List<Color> gradientColors;
  final bool showStroke;
  final Color? strokeColor;
  final double strokeWidth;

  const _GradientIcon({
    required this.assetPath,
    required this.width,
    required this.height,
    required this.gradientColors,
    this.showStroke = false,
    this.strokeColor,
    this.strokeWidth = 1.5,
  });

  @override
  Widget build(BuildContext context) {
    if (showStroke && strokeColor != null) {
      return Stack(
        alignment: Alignment.center,
        children: [
          // Stroke layer
          SvgPicture.asset(
            assetPath,
            width: width,
            height: height,
            color: strokeColor,
          ),
          // Gradient fill layer (slightly smaller to show the stroke)
          ShaderMask(
            shaderCallback: (bounds) {
              return LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: gradientColors,
              ).createShader(bounds);
            },
            child: SvgPicture.asset(
              assetPath,
              width: width - (strokeWidth * 2),
              height: height - (strokeWidth * 2),
              color: Colors.white, // This will be masked by the gradient
            ),
          ),
        ],
      );
    }

    // Default gradient without stroke
    return ShaderMask(
      shaderCallback: (bounds) {
        return LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: gradientColors,
        ).createShader(bounds);
      },
      child: SvgPicture.asset(
        assetPath,
        width: width,
        height: height,
        color: Colors.white, // This will be masked by the gradient
      ),
    );
  }
}

// Updated UserProfileBanner Widget - Fixed overflow issues
class UserProfileBanner extends StatelessWidget {
  const UserProfileBanner({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    
    return LayoutBuilder(
      builder: (context, constraints) {
        final bannerHeight = isTablet ? 150.0 : 130.0;
        final imageSize = isTablet ? 140.0 : 120.0;
        
        return Container(
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? 24 : 15, 
            vertical: isTablet ? 16 : 4  // Reduced vertical padding for phone
          ),
          height: bannerHeight,
          width: constraints.maxWidth, // Use available width
          decoration: BoxDecoration(
            color: const Color(0xFFD90019),
            borderRadius: BorderRadius.circular(5),
          ),
          child: Row(
            children: [
              // Left Side Text - Flexible to prevent overflow
              Expanded(
                flex: isTablet ? 2 : 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min, // Important: minimize column size
                  children: [
                    Flexible(
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          'Invite Friends,',
                          style: TextStyle(
                            fontSize: isTablet ? 24 : 20,
                            fontWeight: FontWeight.w900,
                            fontFamily: 'Solitreo',
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: isTablet ? 8 : 4), // Reduced spacing for phone
                    Flexible(
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          'There Are Rewards',
                          style: TextStyle(
                            fontSize: isTablet ? 16 : 14,
                            fontWeight: FontWeight.w600,
                            fontFamily: 'Solitreo',
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: isTablet ? 16 : 8), // Reduced spacing for phone
                    const Flexible(child: InviteNowButton()) // Make button flexible
                  ],
                ),
              ),

              // Right Side SVG Image - Fixed size to prevent overflow
              const SizedBox(width: 15),
              SizedBox(
                width: imageSize,
                height: imageSize,
                child: SvgPicture.asset(
                  'assets/images/InviteFriendsBanner.svg',
                  fit: BoxFit.contain,
                ),
              ),
            ],
          ),
        );
      }
    );
  }
}

// Updated InviteNowButton Widget with responsive sizing
class InviteNowButton extends StatelessWidget {
  const InviteNowButton({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFFFBF00),
        borderRadius: BorderRadius.circular(15),
      ),
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? 16 : 10, 
        vertical: isTablet ? 8 : 5
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Invite Now!',
            style: TextStyle(
              fontSize: isTablet ? 15 : 13,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          const SizedBox(width: 8),
          SvgPicture.asset(
            'assets/icons/Click.svg',
            height: isTablet ? 24 : 20,
            width: isTablet ? 24 : 20,
          ),
        ],
      ),
    );
  }
}

// Profile Section Widget with responsive sizing
class ProfileSection extends StatelessWidget {
  final String username;
  final String points;
  final String cardCount;
  final String? profileImagePath;
  final bool showEditButton;
  final VoidCallback? onEditTap;

  const ProfileSection({
    super.key,
    required this.username,
    required this.points,
    required this.cardCount,
    this.profileImagePath,
    this.showEditButton = true,
    this.onEditTap,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    
    return Column(
      children: [
        // Profile picture with edit button
        _buildProfilePicture(isTablet),
        const SizedBox(height: 8),
        
        // Username
        Text(
          username,
          style: TextStyle(
            fontSize: isTablet ? 22 : 18, 
            fontWeight: FontWeight.bold
          ),
        ),
        
        // Stats container
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: isTablet ? 24 : 16, 
              vertical: isTablet ? 12 : 6
            ),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.7),
              borderRadius: BorderRadius.circular(12),
            ),
            child: SizedBox(
              width: isTablet ? 280 : 220,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _StatBox(title: points, subtitle: 'Points', isTablet: isTablet),
                  SizedBox(width: isTablet ? 32 : 24),
                  _StatBox(title: cardCount, subtitle: 'Card Count', isTablet: isTablet),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfilePicture(bool isTablet) {
    final radius = isTablet ? 50.0 : 40.0;
    final iconSize = isTablet ? 100.0 : 80.0;
    
    final profileWidget = profileImagePath != null
        ? CircleAvatar(
            radius: radius,
            backgroundImage: AssetImage(profileImagePath!),
          )
        : SvgPicture.asset(
            'assets/icons/Empty Profile.svg',
            width: iconSize,
            height: iconSize,
            color: Colors.black,
          );

    return showEditButton
        ? Stack(
            alignment: Alignment.bottomRight,
            children: [
              profileWidget,
              Positioned(
                bottom: 0,
                right: 0,
                child: GestureDetector(
                  onTap: onEditTap ?? () {},
                  child: Container(
                    padding: EdgeInsets.all(isTablet ? 6 : 4),
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                    ),
                    child: Icon(
                      Icons.create,
                      size: isTablet ? 20 : 16,
                      color: Colors.black,
                    ),
                  ),
                ),
              ),
            ],
          )
        : profileWidget;
  }
}

// Stats Box Widget with responsive text
class _StatBox extends StatelessWidget {
  final String title, subtitle;
  final bool isTablet;

  const _StatBox({
    required this.title,
    required this.subtitle,
    this.isTablet = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: isTablet ? 24 : 20,
            fontWeight: FontWeight.bold,
            color: Colors.black,
          ),
        ),
        Text(
          subtitle,
          style: TextStyle(
            fontSize: isTablet ? 16 : 14,
            color: Colors.black
          ),
        ),
      ],
    );
  }
}

// Updated Action Icon Widget with responsive sizing
class _ActionIcon extends StatelessWidget {
  final String title;
  final String imagePath;

  const _ActionIcon({required this.title, required this.imagePath});

  // Define the gradient colors
  static const List<Color> gradientColors = [
    Color(0xFFFFBF00), // FFBF00
    Color(0xFFFFCC2E), // FFCC2E
    Color(0xFFFFDB62), // FFDB62
    Color(0xFFFFE78B), // FFE78B
    Color(0xFFFFF0A9), // FFF0A9
    Color(0xFFFFF5BB), // FFF5BB
    Color(0xFFFFF7C1), // FFF7C1
  ];

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final iconSize = screenWidth > 600 ? 40.0 : 32.0;
    final textSize = screenWidth > 600 ? 14.0 : 12.0;
    
    return Column(
      children: [
        _GradientIcon(
          assetPath: imagePath,
          width: iconSize,
          height: iconSize,
          gradientColors: gradientColors,
          showStroke: true,
          strokeColor: const Color(0xFFFFBF00),
          strokeWidth: 1.5,
        ),
        const SizedBox(height: 4),
        Text(
          title, 
          style: TextStyle(fontSize: textSize),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

// Info Card Widget with responsive sizing
class _InfoCard extends StatelessWidget {
  final String title, value, imagePath;

  const _InfoCard({
    required this.title,
    required this.value,
    required this.imagePath,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final iconSize = screenWidth > 600 ? 30.0 : 25.0;
    final valueSize = screenWidth > 600 ? 18.0 : 16.0;
    final titleSize = screenWidth > 600 ? 16.0 : 14.0;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            title, 
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: titleSize
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SvgPicture.asset(
                imagePath,
                width: iconSize,
                height: iconSize,
                color: Colors.red,
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: valueSize,
                    fontWeight: FontWeight.bold,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// Updated Feature Item Widget with responsive sizing and better text handling
class _FeatureItem extends StatelessWidget {
  final String imagePath;
  final String label;

  const _FeatureItem({required this.imagePath, required this.label});

  // Define the gradient colors
  static const List<Color> gradientColors = [
    Color(0xFFFFBF00), // FFBF00
    Color(0xFFFFCC2E), // FFCC2E
    Color(0xFFFFDB62), // FFDB62
    Color(0xFFFFE78B), // FFE78B
    Color(0xFFFFF0A9), // FFF0A9
    Color(0xFFFFF5BB), // FFF5BB
    Color(0xFFFFF7C1), // FFF7C1
  ];

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final iconSize = screenWidth > 600 ? 56.0 : 48.0;
    final textSize = screenWidth > 600 ? 12.0 : 11.0;
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _GradientIcon(
          assetPath: imagePath,
          width: iconSize,
          height: iconSize,
          gradientColors: gradientColors,
          showStroke: true,
          strokeColor: const Color(0xFFFFBF00),
          strokeWidth: 1.5,
        ),
        const SizedBox(height: 8),
        Expanded(
          child: Text(
            label,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: textSize),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}