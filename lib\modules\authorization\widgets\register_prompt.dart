import 'package:flutter/material.dart';

class RegisterPrompt extends StatelessWidget {
  final VoidCallback? onRegisterTap;
  final String? questionText;
  final String? actionText;

  const RegisterPrompt({
    Key? key,
    this.onRegisterTap,
    this.questionText,
    this.actionText,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Center(
        child: RichText(
          text: TextSpan(
            text: questionText ?? "Don't have an account? ",
            style: const TextStyle(
              color: Colors.black,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            children: [
              WidgetSpan(
                child: GestureDetector(
                  onTap: onRegisterTap,
                  child: Text(
                    actionText ?? 'Register now',
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
