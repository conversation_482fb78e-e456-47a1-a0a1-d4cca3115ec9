import 'package:flutter/material.dart';

class ProductInfoSection extends StatelessWidget {
  final String price;
  final String? originalPrice;
  final String? discountPercentage;
  final int soldCount;
  final String? pointsOffer;
  final String title;
  final String? variation;
  final bool isFavorite;
  final VoidCallback onFavoritePressed;

  const ProductInfoSection({
    super.key,
    required this.price,
    this.originalPrice,
    this.discountPercentage,
    required this.soldCount,
    this.pointsOffer,
    required this.title,
    this.variation,
    required this.isFavorite,
    required this.onFavoritePressed,
  });

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)

    // Clamp the scale factor to reasonable bounds
    scaleFactor = scaleFactor.clamp(0.8, 1.4);

    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return basePadding; // Small devices
    } else if (screenWidth < 900) {
      return basePadding * 1.2; // Medium devices
    } else {
      return basePadding * 1.5; // Large devices
    }
  }

  @override
  Widget build(BuildContext context) {
    String displayPrice = price.trim().startsWith('RM')
        ? price.trim()
        : 'RM $price';
    String? displayOriginalPrice =
        (originalPrice != null && originalPrice!.trim().isNotEmpty)
        ? (originalPrice!.trim().startsWith('RM')
              ? originalPrice!.trim()
              : 'RM ${originalPrice!.trim()}')
        : null;
    return Padding(
      padding: EdgeInsets.fromLTRB(
        getResponsivePadding(16, context),
        getResponsivePadding(20, context),
        getResponsivePadding(16, context),
        getResponsivePadding(12, context),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Price, original price, discount, favorite
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                flex: 0,
                child: Text(
                  displayPrice,
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(28, context),
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (displayOriginalPrice != null) ...[
                SizedBox(width: getResponsiveFontSize(8, context)),
                Text(
                  displayOriginalPrice,
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(16, context),
                    color: Colors.grey,
                    decoration: TextDecoration.lineThrough,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              if (discountPercentage != null) ...[
                SizedBox(width: getResponsiveFontSize(8, context)),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: getResponsiveFontSize(8, context),
                    vertical: getResponsiveFontSize(2, context),
                  ),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(
                      getResponsiveFontSize(4, context),
                    ),
                  ),
                  child: Text(
                    '-$discountPercentage%',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: getResponsiveFontSize(13, context),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
              const Spacer(),
              IconButton(
                onPressed: onFavoritePressed,
                icon: Icon(
                  isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: isFavorite ? Colors.red : Colors.grey,
                  size: getResponsiveFontSize(24, context),
                ),
              ),
            ],
          ),
          SizedBox(height: getResponsiveFontSize(8, context)),
          // Sold count and points offer
          Row(
            children: [
              Text(
                '$soldCount sold',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(14, context),
                  color: Colors.grey[600],
                ),
              ),
              if (pointsOffer != null) ...[
                SizedBox(width: getResponsiveFontSize(16, context)),
                Text(
                  pointsOffer!,
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    color: Colors.orange[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
          SizedBox(height: getResponsiveFontSize(16, context)),
          // Title
          Text(
            title,
            style: TextStyle(
              fontSize: getResponsiveFontSize(18, context),
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          if (variation != null) ...[
            SizedBox(height: getResponsiveFontSize(8, context)),
            // Variation
            Text(
              'Variation: $variation',
              style: TextStyle(
                fontSize: getResponsiveFontSize(14, context),
                color: Colors.grey[600],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
