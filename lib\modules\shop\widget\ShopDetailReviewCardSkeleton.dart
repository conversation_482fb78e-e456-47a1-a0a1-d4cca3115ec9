import 'package:flutter/material.dart';

class ShopDetailReviewCardSkeleton extends StatelessWidget {
  const ShopDetailReviewCardSkeleton({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final baseColor = Colors.grey.shade300;

    Widget skeleton({double width = double.infinity, double height = 16, BorderRadius? borderRadius}) {
      return Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: baseColor,
          borderRadius: borderRadius ?? BorderRadius.circular(4),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row 1: Avatar, Name, Date
          Row(
            children: [
              // Avatar skeleton
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: baseColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              // Name skeleton
              skeleton(width: 80, height: 16),
              const Spacer(),
              // Date skeleton
              skeleton(width: 50, height: 12),
            ],
          ),
          const SizedBox(height: 6),
          // Row 2: Stars, Helpful
          Row(
            children: [
              Row(
                children: List.generate(
                  5,
                  (index) => Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 1.5),
                    child: Icon(Icons.star, color: baseColor, size: 16),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              skeleton(width: 60, height: 12),
              const SizedBox(width: 2),
              Icon(Icons.thumb_up_alt_rounded, size: 14, color: baseColor),
            ],
          ),
          const SizedBox(height: 4),
          // Row 3: Variation
          skeleton(width: 120, height: 12),
          const SizedBox(height: 6),
          // Row 4: Comment
          skeleton(width: double.infinity, height: 14),
          const SizedBox(height: 6),
          skeleton(width: double.infinity, height: 14),
        ],
      ),
    );
  }
}