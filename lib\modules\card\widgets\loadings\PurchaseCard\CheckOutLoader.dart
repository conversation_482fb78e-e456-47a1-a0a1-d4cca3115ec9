import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../../view/checkOutCard/CheckOutPage.dart';

class CheckOutLoader extends StatefulWidget {
  final int quantity;
  final double total;

  const CheckOutLoader({
    super.key,
    required this.quantity,
    required this.total,
  });

  @override
  State<CheckOutLoader> createState() => _CheckOutLoaderState();
}

class _CheckOutLoaderState extends State<CheckOutLoader> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (_) => CheckoutPage(
              quantity: widget.quantity,
              total: widget.total,
            ),
          ),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final double sideMargin = screenWidth * 0.04; // ~16 on 390px width

    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 70 * (screenWidth / 390),
        elevation: 1,
        shadowColor: Colors.black26,
        backgroundColor: Colors.white,
        title: Shimmer.fromColors(
          baseColor: Colors.grey.shade300,
          highlightColor: Colors.grey.shade100,
          child: Container(
            width: 100 * (screenWidth / 390),
            height: 18 * (screenWidth / 390),
            color: Colors.white,
          ),
        ),
        leading: Shimmer.fromColors(
          baseColor: Colors.grey.shade300,
          highlightColor: Colors.grey.shade100,
          child: Container(
            width: 36 * (screenWidth / 390),
            height: 36 * (screenWidth / 390),
            margin: EdgeInsets.all(8 * (screenWidth / 390)),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(sideMargin),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Total Payment shimmer
            Shimmer.fromColors(
              baseColor: Colors.grey.shade300,
              highlightColor: Colors.grey.shade100,
              child: Container(
                width: double.infinity,
                height: 70 * (screenWidth / 390),
                margin: EdgeInsets.only(bottom: 16 * (screenWidth / 390)),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
            ),
            // Payment Methods shimmer
            Shimmer.fromColors(
              baseColor: Colors.grey.shade300,
              highlightColor: Colors.grey.shade100,
              child: Container(
                width: double.infinity,
                height: 110 * (screenWidth / 390),
                margin: EdgeInsets.only(bottom: 24 * (screenWidth / 390)),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
            ),
            // Billing Info shimmer
            Shimmer.fromColors(
              baseColor: Colors.grey.shade300,
              highlightColor: Colors.grey.shade100,
              child: Container(
                width: double.infinity,
                height: 170 * (screenWidth / 390),
                margin: EdgeInsets.only(bottom: 24 * (screenWidth / 390)),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.only(
          left: sideMargin,
          right: sideMargin,
          bottom: 12 * (screenWidth / 390),
          top: 8 * (screenWidth / 390),
        ),
        child: Shimmer.fromColors(
          baseColor: Colors.grey.shade300,
          highlightColor: Colors.grey.shade100,
          child: Container(
            height: 52 * (screenWidth / 390),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
            ),
          ),
        ),
      ),
    );
  }
}