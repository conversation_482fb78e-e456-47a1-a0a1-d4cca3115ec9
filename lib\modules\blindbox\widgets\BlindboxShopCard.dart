import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:cached_network_image/cached_network_image.dart';

class BlindboxShopCard extends StatelessWidget {
  final String title;
  final String price;
  final double rating;
  final int soldCount;
  final String? maxSavings;
  final String? imageUrl;
  final VoidCallback? onTap;
  final VoidCallback? onFavorite;
  final bool isFavorite;
  final bool isGroupBuy;

  const BlindboxShopCard({
    super.key,
    required this.title,
    required this.price,
    required this.rating,
    required this.soldCount,
    this.maxSavings,
    this.imageUrl,
    this.onTap,
    this.onFavorite,
    this.isFavorite = false,
    this.isGroupBuy = false,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final cardWidth = constraints.maxWidth;
        double scale = cardWidth / 180.0;
        scale = scale.clamp(0.85, 1.5);

        return GestureDetector(
          onTap: onTap,
          child: Card(
            elevation: 0,
            margin: const EdgeInsets.all(0),
            color: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(14 * scale),
              side: BorderSide(
                color: Colors.grey.withValues(alpha: 0.5),
                width: 1.3,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image section with favorite button
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(14 * scale),
                    topRight: Radius.circular(14 * scale),
                  ),
                  child: Container(
                    color: Colors.grey[100],
                    width: double.infinity,
                    height: 110 * scale,
                    child: Stack(
                      children: [
                        // Product image with shimmer and cache
                        Positioned.fill(
                          child: imageUrl != null && imageUrl!.isNotEmpty
                              ? CachedNetworkImage(
                                  imageUrl: imageUrl!,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) =>
                                      Shimmer.fromColors(
                                        baseColor: Colors.grey[300]!,
                                        highlightColor: Colors.grey[100]!,
                                        child: Container(
                                          color: Colors.grey[300],
                                        ),
                                      ),
                                  errorWidget: (context, url, error) =>
                                      _buildPlaceholderImage(scale),
                                )
                              : _buildPlaceholderImage(scale),
                        ),
                        // Heart icon
                        Positioned(
                          top: 8 * scale,
                          right: 8 * scale,
                          child: GestureDetector(
                            onTap: onFavorite,
                            child: Container(
                              width: 26 * scale,
                              height: 26 * scale,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.95),
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.08),
                                    blurRadius: 4 * scale,
                                    offset: Offset(0, 1 * scale),
                                  ),
                                ],
                              ),
                              child: Icon(
                                isFavorite
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                size: 15 * scale,
                                color: isFavorite
                                    ? Colors.red
                                    : Colors.grey[600],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Main content
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 10 * scale,
                      vertical: 8 * scale,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Text(
                          title,
                          style: TextStyle(
                            fontSize: 13 * scale,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                            height: 1.2,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (maxSavings != null) ...[
                          SizedBox(height: 3 * scale),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 6 * scale,
                              vertical: 1.5 * scale,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green.withValues(alpha: 0.08),
                              borderRadius: BorderRadius.circular(4 * scale),
                              border: Border.all(
                                color: Colors.green.withValues(alpha: 0.18),
                                width: 0.5 * scale,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.local_offer_outlined,
                                  size: 11 * scale,
                                  color: Colors.green[700],
                                ),
                                SizedBox(width: 3 * scale),
                                Flexible(
                                  child: Text(
                                    'Max can save $maxSavings',
                                    style: TextStyle(
                                      fontSize: 9 * scale,
                                      color: Colors.green[800],
                                      fontWeight: FontWeight.w500,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                        SizedBox(height: 4 * scale),
                        // Price
                        Text(
                          price,
                          style: TextStyle(
                            fontSize: 15 * scale,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                        Spacer(),
                      ],
                    ),
                  ),
                ),
                // Bottom section for rating and sold
                Padding(
                  padding: EdgeInsets.fromLTRB(
                    10 * scale,
                    0,
                    10 * scale,
                    8 * scale,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.star,
                        size: 13 * scale,
                        color: Colors.amber[700],
                      ),
                      SizedBox(width: 2 * scale),
                      Text(
                        rating.toString(),
                        style: TextStyle(
                          fontSize: 10 * scale,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      if (isGroupBuy) ...[
                        SizedBox(width: 6 * scale),
                        Icon(
                          Icons.groups, // Group buy indicator icon
                          size: 14 * scale,
                          color: Colors.grey[700],
                        ),
                      ],
                      Spacer(),
                      Text(
                        '$soldCount Sold',
                        style: TextStyle(
                          fontSize: 12 * scale,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPlaceholderImage(double scale) {
    return Container(
      color: Colors.grey[200],
      child: Center(
        child: Icon(Icons.broken_image, size: 40 * scale, color: Colors.grey),
      ),
    );
  }
}

class BlindboxShopCardShimmer extends StatelessWidget {
  const BlindboxShopCardShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final cardWidth = constraints.maxWidth;
        double scale = cardWidth / 180.0;
        scale = scale.clamp(0.85, 1.5);

        return Card(
          elevation: 0,
          margin: const EdgeInsets.all(0),
          color: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(14 * scale),
            side: BorderSide(
              color: Colors.grey.withValues(alpha: 0.5),
              width: 1.3,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image section shimmer
              ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(14 * scale),
                  topRight: Radius.circular(14 * scale),
                ),
                child: Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    color: Colors.grey[200],
                    width: double.infinity,
                    height: 110 * scale,
                    child: Stack(
                      children: [
                        Center(
                          child: Container(
                            width: 70 * scale,
                            height: 50 * scale,
                            decoration: BoxDecoration(
                              color: Colors.grey[400],
                              borderRadius: BorderRadius.circular(8 * scale),
                            ),
                          ),
                        ),
                        Positioned(
                          top: 8 * scale,
                          right: 8 * scale,
                          child: Container(
                            width: 26 * scale,
                            height: 26 * scale,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.favorite_border,
                              size: 15 * scale,
                              color: Colors.grey[300],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // Main content shimmer
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: 10 * scale,
                    vertical: 10 * scale,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title shimmer
                      Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          width: double.infinity,
                          height: 14 * scale,
                          color: Colors.grey[300]!,
                        ),
                      ),
                      SizedBox(height: 8 * scale),
                      // Badge shimmer
                      Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          width: 80 * scale,
                          height: 16 * scale,
                          decoration: BoxDecoration(
                            color: Colors.grey[300]!,
                            borderRadius: BorderRadius.circular(4 * scale),
                          ),
                        ),
                      ),
                      SizedBox(height: 8 * scale),
                      // Price shimmer
                      Shimmer.fromColors(
                        baseColor: Colors.grey[300]!,
                        highlightColor: Colors.grey[100]!,
                        child: Container(
                          width: 60 * scale,
                          height: 18 * scale,
                          color: Colors.grey[300]!,
                        ),
                      ),
                      Spacer(),
                    ],
                  ),
                ),
              ),
              // Bottom row shimmer
              Padding(
                padding: EdgeInsets.fromLTRB(
                  10 * scale,
                  0,
                  10 * scale,
                  10 * scale,
                ),
                child: Row(
                  children: [
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        width: 30 * scale,
                        height: 12 * scale,
                        color: Colors.grey[300]!,
                      ),
                    ),
                    Spacer(),
                    Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        width: 50 * scale,
                        height: 12 * scale,
                        color: Colors.grey[300]!,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
