import 'package:flutter/material.dart';
import '../view/BlindboxSearchView.dart';

class BlindboxShopViewVM extends ChangeNotifier {
  // Search controller
  late TextEditingController searchController;
  final List<Map<String, dynamic>> allProducts = [
    {
      'title': "<PERSON>'s 3D Visible Window Air Fryer",
      'price': 'RM120.00',
      'rating': 4.9,
      'soldCount': 8,
      'maxSavings': 'GROUP BUY',
      'category': 'Group Order',
      'isDiscounted': false,
      'isGroupBuy': true,
      'imageUrl': 'https://picsum.photos/900/900',
    },
    {
      'title': 'My Lucky Mall Exclusive Snacks Mystery Box',
      'price': 'RM50.00',
      'rating': 4.6,
      'soldCount': 15,
      'maxSavings': 'RM10',
      'category': 'Snacks',
      'isDiscounted': true,
      'imageUrl': 'https://picsum.photos/1000',
    },
    {
      'title': 'My Lucky Mall Exclusive Travel Mystery Box',
      'price': 'RM55.00',
      'rating': 4.6,
      'soldCount': 15,
      'category': 'Travel',
      'isDiscounted': false,
      'imageUrl': 'https://picsum.photos/1100',
    },
    {
      'title': 'My Lucky Mall Exclusive Health Mystery Box',
      'price': 'RM75.00',
      'rating': 4.6,
      'soldCount': 15,
      'category': 'Health',
      'isDiscounted': false,
      'imageUrl': 'https://picsum.photos/1200',
    },
    {
      'title': 'My Lucky Mall Exclusive Skincare Mystery Box',
      'price': 'RM63.00',
      'rating': 4.6,
      'soldCount': 15,
      'maxSavings': 'RM50',
      'category': 'Skincare',
      'isDiscounted': true,
      'imageUrl': 'https://picsum.photos/1300',
    },
    {
      'title': 'My Lucky Mall Exclusive Fashion Mystery Box',
      'price': 'RM80.00',
      'rating': 4.8,
      'soldCount': 20,
      'category': 'Fashion',
      'isDiscounted': false,
      'imageUrl': 'https://picsum.photos/1400',
    },
    {
      'title': 'My Lucky Mall Exclusive Electronics Mystery Box',
      'price': 'RM120.00',
      'rating': 4.7,
      'soldCount': 12,
      'category': 'Electronics',
      'isDiscounted': true,
      'imageUrl': 'https://picsum.photos/1500',
    },
    {
      'title': 'My Lucky Mall Exclusive Beauty Mystery Box',
      'price': 'RM45.00',
      'rating': 4.5,
      'soldCount': 25,
      'category': 'Beauty',
      'isDiscounted': false,
      'imageUrl': 'https://picsum.photos/1600',
    },
    {
      'title': 'My Lucky Mall Exclusive Gaming Mystery Box',
      'price': 'RM95.00',
      'rating': 4.9,
      'soldCount': 18,
      'category': 'Electronics',
      'isDiscounted': true,
      'imageUrl': 'https://picsum.photos/1700',
    },
  ];

  List<Map<String, dynamic>> filteredProducts = [];
  Map<String, dynamic>? currentFilters;
  bool isLoading = true;

  BlindboxShopViewVM() {
    searchController = TextEditingController();
    filteredProducts = List.from(allProducts);
    _simulateLoading();
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  void navigateToSearch(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            BlindboxSearchView(initialSearchQuery: searchController.text),
      ),
    );
  }

  void _simulateLoading() async {
    await Future.delayed(const Duration(milliseconds: 1500));
    isLoading = false;
    notifyListeners();
  }

  void applyFilters(Map<String, dynamic> filters) {
    currentFilters = filters;
    filteredProducts = _filterProducts(allProducts, filters);
    notifyListeners();
  }

  List<Map<String, dynamic>> _filterProducts(
    List<Map<String, dynamic>> products,
    Map<String, dynamic> filters,
  ) {
    List<Map<String, dynamic>> filtered = List.from(products);

    // Filter by price range
    if (filters['minPrice'] != null || filters['maxPrice'] != null) {
      filtered = filtered.where((product) {
        double price = double.parse(
          product['price'].toString().replaceAll('RM', '').trim(),
        );
        bool minCheck =
            filters['minPrice'] == null || price >= filters['minPrice'];
        bool maxCheck =
            filters['maxPrice'] == null || price <= filters['maxPrice'];
        return minCheck && maxCheck;
      }).toList();
    }

    // Filter by category
    if (filters['category'] != null && filters['category'] != 'All') {
      filtered = filtered
          .where((product) => product['category'] == filters['category'])
          .toList();
    }

    // Filter by rating
    if (filters['rating'] != null && filters['rating'] > 0) {
      filtered = filtered
          .where((product) => product['rating'] >= filters['rating'])
          .toList();
    }

    // Filter by discount
    if (filters['showOnlyDiscounted'] == true) {
      filtered = filtered
          .where((product) => product['isDiscounted'] == true)
          .toList();
    }

    // Sort products
    if (filters['sortBy'] != null) {
      switch (filters['sortBy']) {
        case 'Price: Low to High':
          filtered.sort((a, b) {
            double priceA = double.parse(
              a['price'].toString().replaceAll('RM', '').trim(),
            );
            double priceB = double.parse(
              b['price'].toString().replaceAll('RM', '').trim(),
            );
            return priceA.compareTo(priceB);
          });
          break;
        case 'Price: High to Low':
          filtered.sort((a, b) {
            double priceA = double.parse(
              a['price'].toString().replaceAll('RM', '').trim(),
            );
            double priceB = double.parse(
              b['price'].toString().replaceAll('RM', '').trim(),
            );
            return priceB.compareTo(priceA);
          });
          break;
        case 'Rating: High to Low':
          filtered.sort((a, b) => b['rating'].compareTo(a['rating']));
          break;
        case 'Newest':
          // For demo purposes, we'll sort by sold count (assuming newer items have lower sold count)
          filtered.sort((a, b) => a['soldCount'].compareTo(b['soldCount']));
          break;
        case 'Popularity':
        default:
          filtered.sort((a, b) => b['soldCount'].compareTo(a['soldCount']));
          break;
      }
    }

    return filtered;
  }
}
