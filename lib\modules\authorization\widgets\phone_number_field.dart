import 'package:flutter/material.dart';
import 'package:country_flags/country_flags.dart';

class PhoneNumberField extends StatelessWidget {
  final TextEditingController? controller;
  final String? hintText;
  final Function(String)? onChanged;
  final String? Function(String?)? validator;

  const PhoneNumberField({
    Key? key,
    this.controller,
    this.hintText,
    this.onChanged,
    this.validator,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Phone Number Label
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              'Phone Number',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 13),
            ),
          ),
        ),
        const SizedBox(height: 8),
        // Phone Number Field
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24.0),
          child: Container(
            width: double.infinity, // Ensure full width
            height: 48, // Set a fixed height for consistency
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.black),
            ),
            child: Row(
              children: [
                const SizedBox(width: 8),
                CountryFlag.fromCountryCode('MY', height: 22, width: 32),
                const SizedBox(width: 8),
                const Text(
                  '+60',
                  style: TextStyle(fontSize: 15, fontWeight: FontWeight.w500),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: controller,
                    keyboardType: TextInputType.phone,
                    onChanged: onChanged,
                    style: const TextStyle(
                      fontSize: 13,
                    ), // Adjust font size here
                    decoration: InputDecoration(
                      border: InputBorder.none,
                      hintText: hintText ?? 'Please enter your phone number',
                      hintStyle: const TextStyle(color: Colors.grey),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
