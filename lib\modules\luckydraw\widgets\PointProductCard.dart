import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class PointProductCard extends StatelessWidget {
  final String productName;
  final String imageUrl;
  final int pointValue;
  final double rating;
  final int claimsRemaining;
  final int currentParticipants;
  final int maxParticipants;
  final VoidCallback? onTap;

  const PointProductCard({
    Key? key,
    required this.productName,
    required this.imageUrl,
    required this.pointValue,
    required this.rating,
    required this.claimsRemaining,
    required this.currentParticipants,
    required this.maxParticipants,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Responsive sizing with better constraints
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Improved scaling functions with constraints
    double scaleW(double size) =>
        (size * screenWidth / 375).clamp(size * 0.8, size * 1.5);
    double scaleH(double size) =>
        (size * screenHeight / 812).clamp(size * 0.8, size * 1.5);
    double scaleText(double size) =>
        (size * screenWidth / 375).clamp(size * 0.7, size * 1.3);

    // Responsive card width calculation
    final isTablet = screenWidth > 600;
    final crossAxisCount = isTablet ? 3 : 2;
    final horizontalPadding = scaleW(16);
    final spacing = scaleW(12);
    final cardWidth =
        (screenWidth -
            (horizontalPadding * 2) -
            (spacing * (crossAxisCount - 1))) /
        crossAxisCount;

    // Ensure minimum card width
    final finalCardWidth = cardWidth.clamp(140.0, 200.0);
    final imageHeight = finalCardWidth * 0.6;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: finalCardWidth,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(scaleW(12)),
          border: Border.all(
            color: Colors.grey.withOpacity(0.3),
            width: scaleW(1.5).clamp(1.0, 2.0),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              offset: Offset(0, scaleH(2)),
              blurRadius: scaleW(8).clamp(4, 12),
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Container(
              height: imageHeight,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(scaleW(12)),
                  topRight: Radius.circular(scaleW(12)),
                ),
                color: Colors.grey[100],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(scaleW(12)),
                  topRight: Radius.circular(scaleW(12)),
                ),
                child: CachedNetworkImage(
                  imageUrl: imageUrl,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey[100],
                    child: Center(
                      child: SizedBox(
                        width: scaleW(24).clamp(20, 30),
                        height: scaleW(24).clamp(20, 30),
                        child: const CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Color(0xFFFFBF00),
                        ),
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    color: Colors.grey[200],
                    child: Icon(
                      Icons.image_not_supported,
                      color: Colors.grey[400],
                      size: scaleW(40).clamp(30, 50),
                    ),
                  ),
                  memCacheWidth: 400,
                  memCacheHeight: 300,
                ),
              ),
            ),

            // Product Details
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(scaleW(8).clamp(6, 12)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Product Name
                    Text(
                      productName,
                      style: TextStyle(
                        fontSize: scaleText(10).clamp(8, 14),
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                        height: 1.1,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // Bottom section with points and rating
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Points
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: scaleW(5).clamp(4, 8),
                            vertical: scaleH(2).clamp(1, 4),
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xFFFFBF00),
                            borderRadius: BorderRadius.circular(
                              scaleW(3).clamp(2, 5),
                            ),
                          ),
                          child: Text(
                            '$pointValue Pts',
                            style: TextStyle(
                              fontSize: scaleText(10).clamp(8, 12),
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        SizedBox(height: scaleH(4).clamp(2, 6)),

                        // Participation Progress Bar
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: scaleH(2).clamp(1, 3)),
                            // Progress bar
                            Container(
                              height: scaleH(3).clamp(2, 5),
                              decoration: BoxDecoration(
                                color: Colors.grey[300],
                                borderRadius: BorderRadius.circular(
                                  scaleH(1.5).clamp(1, 3),
                                ),
                              ),
                              child: FractionallySizedBox(
                                alignment: Alignment.centerLeft,
                                widthFactor: maxParticipants > 0
                                    ? (currentParticipants / maxParticipants)
                                          .clamp(0.0, 1.0)
                                    : 0.0,
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFFFBF00),
                                    borderRadius: BorderRadius.circular(
                                      scaleH(1.5).clamp(1, 3),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: scaleH(15).clamp(10, 20)),

                        // Rating and Claims
                        Row(
                          children: [
                            // Rating
                            Row(
                              children: [
                                Text(
                                  rating.toString(),
                                  style: TextStyle(
                                    fontSize: scaleText(10).clamp(8, 12),
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black87,
                                  ),
                                ),
                                SizedBox(width: scaleW(1).clamp(1, 2)),
                                Icon(
                                  Icons.star,
                                  size: scaleW(11).clamp(9, 14),
                                  color: const Color(0xFFFFBF00),
                                ),
                              ],
                            ),
                            const Spacer(),

                            // Claims Remaining
                            Flexible(
                              child: Text(
                                '$claimsRemaining Claim',
                                style: TextStyle(
                                  fontSize: scaleText(10).clamp(8, 12),
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black54,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Grid widget to display multiple point product cards
class PointProductGrid extends StatelessWidget {
  final List<Map<String, dynamic>> products;
  final Function(Map<String, dynamic>)? onProductTap;

  const PointProductGrid({Key? key, required this.products, this.onProductTap})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Improved scaling functions
    double scaleW(double size) =>
        (size * screenWidth / 375).clamp(size * 0.8, size * 1.5);
    double scaleH(double size) =>
        (size * screenHeight / 812).clamp(size * 0.8, size * 1.5);

    // Responsive grid configuration
    int crossAxisCount;
    double childAspectRatio;

    if (screenWidth > 900) {
      // Large tablets/desktop
      crossAxisCount = 4;
      childAspectRatio = 0.85;
    } else if (screenWidth > 600) {
      // Tablets
      crossAxisCount = 3;
      childAspectRatio = 0.8;
    } else if (screenWidth > 400) {
      // Large phones
      crossAxisCount = 2;
      childAspectRatio = 0.75;
    } else {
      // Small phones
      crossAxisCount = 2;
      childAspectRatio = 0.7;
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.all(scaleW(16).clamp(12, 20)),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: scaleW(12).clamp(8, 16),
        mainAxisSpacing: scaleH(12).clamp(8, 16),
        childAspectRatio: childAspectRatio,
      ),
      itemCount: products.length,
      itemBuilder: (context, index) {
        final product = products[index];
        return PointProductCard(
          productName: product['productName'] ?? '',
          imageUrl: product['imageUrl'] ?? '',
          pointValue: product['pointValue'] ?? 0,
          rating: (product['rating'] ?? 0.0).toDouble(),
          claimsRemaining: product['claimsRemaining'] ?? 0,
          currentParticipants: product['currentParticipants'] ?? 0,
          maxParticipants: product['maxParticipants'] ?? 100,
          onTap: () => onProductTap?.call(product),
        );
      },
    );
  }
}
