import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';
import '../../modules/nav/tabItem.dart'; // to access TabItem enum

class BottomNavBar extends StatefulWidget {
  final TabItem currentTab;
  final TabItem? justTappedTab;
  final ValueChanged<TabItem> onSelectTab;

  const BottomNavBar({
    Key? key,
    required this.currentTab,
    required this.justTappedTab,
    required this.onSelectTab,
  }) : super(key: key);

  @override
  State<BottomNavBar> createState() => _BottomNavBarState();
}

class _BottomNavBarState extends State<BottomNavBar> {
  final Map<TabItem, bool> _hoverStates = {
  for (var tab in TabItem.values) tab: false,
  };

  final Map<TabItem, bool> _isAnimating = {
    for (var tab in TabItem.values) tab: false,
  };

  @override
  void didUpdateWidget(covariant BottomNavBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.justTappedTab != null) {
      setState(() {
        _isAnimating[widget.justTappedTab!] = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: TabItem.values.indexOf(widget.currentTab),
      onTap: (index) {
        final selected = TabItem.values[index];
        widget.onSelectTab(selected);
      },
      selectedItemColor: Colors.amber,
      unselectedItemColor: const Color.fromRGBO(153, 153, 153, 1),
      items: TabItem.values.map((tab) {
        final isSelected = widget.currentTab == tab;
        final assetBase  = tab.assetName;

        final String iconPath =
            'assets/icons/nav/${assetBase}${isSelected ? 'Active' : 'Inactive'}.svg';

        final String lottiePath =
            'assets/lottie/IconAnimation/${assetBase}.json';

        return BottomNavigationBarItem(
          label: assetBase [0].toUpperCase() + assetBase .substring(1),
          icon: _isAnimating[tab] == true
              ? Lottie.asset(
                  lottiePath,
                  width: 30,
                  height: 30,
                  repeat: false,
                  errorBuilder: (context, error, stackTrace) => const Icon(
                    Icons.error,
                    color: Colors.red,
                    size: 28,
                  ),
                  onLoaded: (composition) {
                    Future.delayed(composition.duration, () {
                      if (mounted) {
                        setState(() => _isAnimating[tab] = false);
                      }
                    });
                  },
                )
              : MouseRegion(
                onEnter: (_) => setState(() => _hoverStates[tab] = true),
                onExit: (_) => setState(() => _hoverStates[tab] = false),
                child: SvgPicture.asset(
                  iconPath,
                  width: 28,
                  height: 28,
                  color: isSelected
                      ? Colors.amber
                      : _hoverStates[tab] == true
                          ? Colors.amber.shade200
                          : const Color(0xFF999999),
                ),
              
        ),
        );
      }).toList(),
    );
  }
}