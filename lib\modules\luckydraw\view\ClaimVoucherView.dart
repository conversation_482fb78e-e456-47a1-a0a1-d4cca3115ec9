import 'package:flutter/material.dart';
import 'package:luckymall/modules/luckydraw/widgets/customAppBar.dart';
import 'package:luckymall/modules/luckydraw/widgets/VoucherCard.dart';
import 'package:luckymall/modules/luckydraw/widgets/PaymentDetails.dart';
import 'package:luckymall/modules/luckydraw/widgets/ClaimNotes.dart';
import 'package:luckymall/modules/luckydraw/view/ClaimVoucherNavView.dart';

class ClaimVoucherView extends StatelessWidget {
  const ClaimVoucherView({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375; // base width 375
    double scaleH(double value) =>
        value * screenHeight / 812; // base height 812
    double scaleText(double value) => value * screenWidth / 375;

    return Scaffold(
      appBar: CustomAppBar(title: 'Claim Vouchers'),
      backgroundColor: Color.fromARGB(255, 231, 231, 231),
      body: Column(
        children: [
          SizedBox(height: scaleH(16)),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: scaleW(16)),
            child: VoucherCard(),
          ),
          SizedBox(height: scaleH(12)),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: scaleW(16)),
            child: PaymentDetails(),
          ),
          SizedBox(height: scaleH(12)),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: scaleW(20)),
            child: ClaimNotes(),
          ),
          const Spacer(),
        ],
      ),
      bottomNavigationBar: Container(
        padding: EdgeInsets.symmetric(
          horizontal: scaleW(16),
          vertical: scaleH(8),
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: scaleW(8),
              offset: Offset(0, -scaleH(2)),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: RichText(
                text: TextSpan(
                  text: 'Total ',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: scaleText(16),
                  ),
                  children: [
                    TextSpan(
                      text: 'RM0.00',
                      style: TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                        fontSize: scaleText(16),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              width: scaleW(120),
              height: scaleH(40),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFFFBF00),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(scaleW(8)),
                  ),
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ClaimVoucherNav(),
                    ),
                  );
                },
                child: Text(
                  'Claim',
                  style: TextStyle(
                    fontSize: scaleText(16),
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
