import 'package:flutter/material.dart';
import '../widgets/PurchaseCard/BottomBar.dart';
import '../widgets/PurchaseCard/CategoryButtonsRow.dart';
import '../widgets/PurchaseCard/CategoryDescriptionBox.dart';
import '../widgets/PurchaseCard/ImageSliderWithDots.dart';
import '../widgets/PurchaseCard/PriceAndQuantitySection.dart';
import '../widgets/InfoDialog/PurchaseGuidelineDialog.dart';
import '../widgets/CategorySelectionSheet.dart';
import '../widgets/loadings/PurchaseCard/CheckOutLoader.dart';

class PurchaseCardPage extends StatefulWidget {
  const PurchaseCardPage({super.key});

  @override
  State<PurchaseCardPage> createState() => _PurchaseCardPageState();
}

class _PurchaseCardPageState extends State<PurchaseCardPage> {
  final List<String> imageUrls = [
    // 'https://via.placeholder.com/390x334.png?text=Card+1',
    // 'https://via.placeholder.com/390x334.png?text=Card+2',
    // 'https://via.placeholder.com/390x334.png?text=Card+3',
    // 'https://via.placeholder.com/390x334.png?text=Card+4',
    'assets/images/card/backgroundCard/cardWallpaper02.png',
    'assets/images/card/imageSlider/example3.png',
    'assets/images/card/imageSlider/example1.jpg',
    'assets/images/card/imageSlider/example2.jpeg',
  ];

  int quantity =5;
  final double unitPrice = 1.00;
  int currentIndex = 0;
  String selectedCategory = 'Fantasy';

  void _updateQuantity(int newQuantity) {
      setState(() {
        quantity = newQuantity;
      }); 
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    const sliderHeight = 334.0;
    double total = quantity * unitPrice;

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 70,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Card Collection - Surprise Attack',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w700),
        ),
        backgroundColor: Colors.white,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {},
          ),
        ],
      ),
      body: Column(
        children: [
          ImageSliderWithDots(
            imageUrls: imageUrls,
            currentIndex: currentIndex, // Add this line
            onPageChanged: (i) => setState(() => currentIndex = i)),
          CategoryDescriptionBox(category: selectedCategory),
          PriceAndQuantitySection(
            quantity: quantity,
            onQuantityChanged: _updateQuantity,
            unitPrice: unitPrice,
          ),
          const SizedBox(height: 16),
          CategoryButtonsRow(
            onGuidelinesPressed: () {
                showDialog(
                  context: context, 
                  builder: (context)=> const PurchaseGuidelineDialog(),
                );
            },
            onSelectCategoryPressed: () {
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                ),
                builder: (context) {
                  return CategorySelectionSheet(
                    onCategorySelected:(selectedCategory) {
                      setState(() {
                        this.selectedCategory = selectedCategory;

                      });
                    }
                  );
                }
              ); 
            },
          ),
          const SizedBox(height: 8), // Extra spacing before bottom bar
        ],
      ),
      bottomNavigationBar: BottomBar(
        totalPrice: total,
        onBuyNowPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => CheckOutLoader(
                quantity: quantity,
                total: total,
              ),
            ),
          );
        },
      ),
    );
  }

}

