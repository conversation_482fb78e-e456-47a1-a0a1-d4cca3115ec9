import 'package:flutter/material.dart';
import 'widgets/UpdateAddress.dart';
import 'widgets/DeleteConfirmationDialog.dart';
import 'widgets/UpdateAddress.dart';

class UpdateAddressView extends StatefulWidget {
  final Map<String, dynamic> address;

  const UpdateAddressView({Key? key, required this.address}) : super(key: key);

  @override
  State<UpdateAddressView> createState() => _UpdateAddressViewState();
}

class _UpdateAddressViewState extends State<UpdateAddressView> {
  late TextEditingController nameController;
  late TextEditingController phoneController;
  late TextEditingController postalController;
  late TextEditingController addressController;
  bool isDefault = false;

  @override
  void initState() {
    super.initState();
    nameController = TextEditingController(text: widget.address['name']);
    phoneController = TextEditingController(text: widget.address['phone']);
    postalController = TextEditingController(text: widget.address['postal']);
    addressController = TextEditingController(text: widget.address['address']);
    isDefault = widget.address['isDefault'] ?? false;
  }

  @override
  void dispose() {
    nameController.dispose();
    phoneController.dispose();
    postalController.dispose();
    addressController.dispose();
    super.dispose();
  }

  void showDeleteConfirmationDialog() {
    showDialog(
      context: context,
      builder: (context) => DeleteConfirmationDialog(
        onDeleteConfirmed: () {
          Navigator.of(context).pop(); // close dialog
          Navigator.of(context).pop(); // go back to address view
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Update Address',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w500,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 1,
      ),
      body: UpdateAddressForm(
        nameController: nameController,
        phoneController: phoneController,
        postalController: postalController,
        addressController: addressController,
        isDefault: isDefault,
        onDefaultChanged: (value) {
          setState(() {
            isDefault = value;
          });
        },
        onDelete: showDeleteConfirmationDialog,
        onSubmit: () {
          // Save logic here
        },
      ),
      backgroundColor: Colors.white,
    );
  }
}
