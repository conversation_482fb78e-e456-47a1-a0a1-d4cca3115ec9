import 'package:flutter/material.dart';
import '../view/ParticipationRecordsView.dart';

class FailedJoinPopup extends StatelessWidget {
  final VoidCallback? onParticipantRecord;
  final VoidCallback? onClose;
  final int returnPoint;
  final double? imageHeight;
  final double? imageWidth;
  final BoxFit imageFit;

  const FailedJoinPopup({
    Key? key,
    this.onParticipantRecord,
    this.onClose,
    required this.returnPoint,
    this.imageHeight,
    this.imageWidth,
    this.imageFit = BoxFit.cover,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black54,
      child: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Top section with red background
              Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  color: Color(0xFFFF8FA3), // Red background
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                padding: const EdgeInsets.all(24),
                child: Center(
                  child: Container(
                    width: imageWidth ?? 150, // Fixed width
                    height: imageHeight ?? 150, // Fixed height
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.asset(
                        'assets/images/failed_join.png', // Fixed asset image
                        fit: imageFit,
                      ),
                    ),
                  ),
                ),
              ),
              // Bottom section with white background
              Container(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Failed text
                    const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '❌ FAILED to Join!',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 50),
                    RichText(
                      text: TextSpan(
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black,
                        ),
                        children: [
                          const TextSpan(text: 'Returned Points: '),
                          TextSpan(
                            text: '$returnPoint points',
                            style: const TextStyle(
                              color: Color(0xFFFF3B30), // Red
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Participant Record button
                    SizedBox(
                      width: double.infinity,
                      height: 48,
                      child: ElevatedButton(
                        onPressed:
                            onParticipantRecord ??
                            () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const ParticipationRecordsView(),
                                ),
                              );
                            },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(
                            0xFFFFBF00,
                          ), // Yellow background
                          foregroundColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          'Participant Record',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Close button
                    GestureDetector(
                      onTap: onClose ?? () => Navigator.of(context).pop(),
                      child: const Text(
                        'Close',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class FailedJoinPopupOverlay {
  static void show({
    required BuildContext context,
    VoidCallback? onParticipantRecord,
    VoidCallback? onClose,
    required int returnPoint,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return FailedJoinPopup(
          onParticipantRecord: onParticipantRecord,
          onClose: onClose ?? () => Navigator.of(context).pop(),
          returnPoint: returnPoint,
          imageHeight: 150,
          imageWidth: 150,
        );
      },
    );
  }
}
