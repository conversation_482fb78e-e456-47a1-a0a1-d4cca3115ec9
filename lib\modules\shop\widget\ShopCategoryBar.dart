import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../data/model/ShoplistModel.dart';

class ShopCategoryBar extends StatelessWidget {
  final List<ShopCategory> categories;
  final void Function(String) onCategoryTap;
  final IconData Function(String) getIconFromString;

  const ShopCategoryBar({
    Key? key,
    required this.categories,
    required this.onCategoryTap,
    required this.getIconFromString,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFFFE066),
            Colors.white,
          ],
          stops: [0.0, 1],
        ),
      ),
      padding: const EdgeInsets.fromLTRB(10, 16, 16, 8),
      child: SizedBox(
        height: 110,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          itemCount: categories.length,
          separatorBuilder: (_, __) => const SizedBox(width: 8),
          itemBuilder: (context, index) {
            final category = categories[index];
            return GestureDetector(
              onTap: () => onCategoryTap(category.id),
              child: SizedBox(
                width: 72,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: category.isSelected
                            ? Colors.amber[700]
                            : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.amber,
                          width: 2,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: category.id == 'all'
                            ? Icon(
                                Icons.grid_view,
                                color: category.isSelected
                                    ? Colors.black
                                    : Colors.amber[700],
                                size: 28,
                              )
                            : SvgPicture.asset(
                                'assets/icons/shop/${category.icon}',
                                color: category.isSelected
                                    ? Colors.black
                                    : Colors.amber[700],
                                width: 28,
                                height: 28,
                              ),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      height: 32,
                      alignment: Alignment.topCenter,
                      child: Text(
                        category.name,
                        style: TextStyle(
                          fontSize: 11,
                          color: category.isSelected
                              ? Colors.black87
                              : Colors.black,
                          fontWeight: category.isSelected
                              ? FontWeight.w600
                              : FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}