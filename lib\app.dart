import 'package:flutter/material.dart';
import 'component/navigation/bottomNavBar.dart';
import 'modules/nav/tabItem.dart';
import 'modules/card/view/CardPage.dart';
import 'modules/shop/view/ShoplistView.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  TabItem _currentTab = TabItem.shop;
  TabItem? _justTapped;

  void _onTabSelected(TabItem selectedTab) {
    setState(() {
      _justTapped = selectedTab;
      _currentTab = selectedTab;
    });
  }

  Widget _buildPage(TabItem tab) {
    switch (tab) {
      case TabItem.shop:
        return const ShoplistView();
      case TabItem.card:
        return const CardPage();
      case TabItem.luckyDraw:
        return _tempPage('Lucky Draw');
      case TabItem.blindBox:
        return _tempPage('Blind Box');
      case TabItem.me:
        return _tempPage('Me');
    }
  }

  Widget _tempPage(String title) {
    return Center(
      child: Text(
        '$title Page (Placeholder)',
        style: const TextStyle(fontSize: 24, color: Colors.grey),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        body: _buildPage(_currentTab),
        bottomNavigationBar: BottomNavBar(
          currentTab: _currentTab,
          justTappedTab: _justTapped,
          onSelectTab: _onTabSelected,
        ),
      ),
    );
  }
}