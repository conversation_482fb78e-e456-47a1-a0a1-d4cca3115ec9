import 'package:flutter/material.dart';

class BlindboxFilterOverlay extends StatelessWidget {
  final Function(Map<String, double?>)? onFilterApplied;
  final Map<String, dynamic>? initialFilters;

  const BlindboxFilterOverlay({
    Key? key,
    this.onFilterApplied,
    this.initialFilters,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final minController = TextEditingController(
      text: initialFilters?['minPrice']?.toString() ?? '',
    );
    final maxController = TextEditingController(
      text: initialFilters?['maxPrice']?.toString() ?? '',
    );

    void reset() {
      minController.clear();
      maxController.clear();
    }

    void confirm() {
      final min = minController.text.isNotEmpty
          ? double.tryParse(minController.text)
          : null;
      final max = maxController.text.isNotEmpty
          ? double.tryParse(maxController.text)
          : null;
      onFilterApplied?.call({'minPrice': min, 'maxPrice': max});
      Navigator.of(context).pop();
    }

    double getResponsiveFontSize(double baseSize) {
      double screenWidth = MediaQuery.of(context).size.width;
      double scaleFactor = screenWidth / 375;
      scaleFactor = scaleFactor.clamp(0.8, 1.4);
      return baseSize * scaleFactor;
    }

    double getResponsivePadding(double basePadding) {
      double screenWidth = MediaQuery.of(context).size.width;
      if (screenWidth < 600) {
        return basePadding;
      } else if (screenWidth < 900) {
        return basePadding * 1.2;
      } else {
        return basePadding * 1.5;
      }
    }

    return Container(
      padding: EdgeInsets.fromLTRB(
        getResponsivePadding(20),
        getResponsivePadding(20),
        getResponsivePadding(20),
        getResponsivePadding(32),
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Price Range Filter (RM)',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: getResponsiveFontSize(18),
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Preset buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _RangeButton(
                label: '0 - 50',
                onTap: () {
                  minController.text = '0';
                  maxController.text = '50';
                },
              ),
              _RangeButton(
                label: '50 - 100',
                onTap: () {
                  minController.text = '50';
                  maxController.text = '100';
                },
              ),
              _RangeButton(
                label: '100 - 150',
                onTap: () {
                  minController.text = '100';
                  maxController.text = '150';
                },
              ),
              _RangeButton(
                label: '150 above',
                onTap: () {
                  minController.text = '150';
                  maxController.text = '';
                },
              ),
            ],
          ),
          const SizedBox(height: 24),
          // Custom range input
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              'Custom Range Input',
              style: TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              // MIN input
              Expanded(
                child: Container(
                  height: 40,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFF3CD),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  alignment: Alignment.center,
                  child: TextField(
                    controller: minController,
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      hintText: 'MIN',
                      hintStyle: TextStyle(color: Colors.black54),
                      contentPadding: EdgeInsets.zero,
                    ),
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Container(width: 24, height: 2, color: Colors.grey[300]),
              const SizedBox(width: 16),
              // MAX input
              Expanded(
                child: Container(
                  height: 40,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFF3CD),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  alignment: Alignment.center,
                  child: TextField(
                    controller: maxController,
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      hintText: 'MAX',
                      hintStyle: TextStyle(color: Colors.black54),
                      contentPadding: EdgeInsets.zero,
                    ),
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              // Reset button (gray)
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    reset();
                  },
                  style: OutlinedButton.styleFrom(
                    backgroundColor: Colors.grey[300],
                    foregroundColor: Colors.black,
                    side: BorderSide.none,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Reset',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Confirm button (amber)
              Expanded(
                child: ElevatedButton(
                  onPressed: confirm,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'Confirm',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _RangeButton extends StatelessWidget {
  final String label;
  final VoidCallback onTap;
  const _RangeButton({required this.label, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
        decoration: BoxDecoration(
          color: const Color(0xFFFFF3CD),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(label, style: const TextStyle(color: Colors.black)),
      ),
    );
  }
}
