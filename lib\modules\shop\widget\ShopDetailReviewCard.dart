import 'package:flutter/material.dart';
import '../../../data/model/ShopDetailModel.dart';

class ShopDetailReviewCard extends StatelessWidget {
  final ProductReview review;
  const ShopDetailReviewCard({Key? key, required this.review}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Responsive font scaling
    final double screenWidth = MediaQuery.of(context).size.width;
    final double baseFontSize = screenWidth > 800 ? 18 : 14;
    final double smallFontSize = screenWidth > 800 ? 15 : 12;

    return Container(
      margin: const EdgeInsets.only(bottom: 18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row 1: Avatar, Name, Date
          Row(
            children: [
              const CircleAvatar(
                radius: 16,
                backgroundColor: Colors.grey,
                child: Icon(Icons.person, size: 18, color: Colors.white),
              ),
              const SizedBox(width: 8),
              Text(
                review.userName,
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: smallFontSize),
              ),
              const Spacer(),
              Text(
                review.date,
                style: TextStyle(color: Colors.grey, fontSize: smallFontSize),
              ),
            ],
          ),
          const SizedBox(height: 6),
          // Row 2: Stars, Helpful
          Row(
            children: [
              Row(
                children: List.generate(
                  5,
                  (index) => Icon(
                    index < review.rating ? Icons.star : Icons.star_border,
                    color: Colors.amber[700],
                    size: 16,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Helpful (${review.helpfulCount})',
                style: TextStyle(fontSize: smallFontSize, color: Colors.grey, fontWeight: FontWeight.w500),
              ),
              const SizedBox(width: 2),
              const Icon(Icons.thumb_up_alt_rounded, size: 14, color: Colors.amber),
            ],
          ),
          const SizedBox(height: 4),
          // Row 3: Variation
          Text(
            'Variation: ${review.variation}',
            style: TextStyle(fontSize: smallFontSize, color: Colors.grey),
          ),
          const SizedBox(height: 6),
          // Row 4: Comment
          Text(
            review.comment,
            style: TextStyle(fontSize: baseFontSize, color: Colors.black87),
          ),
        ],
      ),
    );
  }
}