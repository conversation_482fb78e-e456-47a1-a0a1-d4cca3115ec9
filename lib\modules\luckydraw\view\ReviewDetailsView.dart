import 'package:flutter/material.dart';
import 'package:luckymall/modules/luckydraw/widgets/customAppBar.dart';
import 'package:luckymall/modules/luckydraw/widgets/ReviewCategory.dart';
import 'package:luckymall/modules/luckydraw/widgets/AllReviewCards.dart';

class ReviewDetailsView extends StatefulWidget {
  const ReviewDetailsView({super.key});

  @override
  State<ReviewDetailsView> createState() => _ReviewDetailsViewState();
}

class _ReviewDetailsViewState extends State<ReviewDetailsView> {
  String selectedCategory = 'All';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'All Reviews'),
      body: Column(
        children: [
          // Category Filter
          Container(
            padding: const EdgeInsets.symmetric(vertical: 2),
            child: ReviewCategory(
              onCategorySelected: (category) {
                setState(() {
                  selectedCategory = category;
                });
              },
              initialCategory: selectedCategory,
            ),
          ),

          // All Reviews
          Expanded(child: AllReviewCards(selectedCategory: selectedCategory)),
        ],
      ),
    );
  }
}
