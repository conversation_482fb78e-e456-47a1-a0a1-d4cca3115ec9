import 'package:flutter/material.dart';

class UserProfileView extends StatelessWidget {
  const UserProfileView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Colors
    const gold = Color(0xFFFFC107);
    const red = Color(0xFFFF1744);

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Header with background
              Stack(
                children: [
                  Container(
                    height: 180,
                    width: double.infinity,
                    decoration: const BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage('assets/images/profile_bg.png'), // Replace with your bg
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 16,
                    right: 16,
                    child: Row(
                      children: const [
                        Icon(Icons.visibility, color: Colors.black54),
                        SizedBox(width: 12),
                        Icon(Icons.notifications_none, color: Colors.black54),
                        SizedBox(width: 12),
                        Icon(Icons.settings, color: Colors.black54),
                      ],
                    ),
                  ),
                  Positioned(
                    left: 0,
                    right: 0,
                    top: 110,
                    child: <PERSON>umn(
                      children: [
                        Stack(
                          alignment: Alignment.bottomRight,
                          children: [
                            const CircleAvatar(
                              radius: 38,
                              backgroundColor: Colors.white,
                              child: CircleAvatar(
                                radius: 35,
                                backgroundImage: AssetImage('assets/images/avatar.png'), // Replace with your avatar
                              ),
                            ),
                            Positioned(
                              bottom: 2,
                              right: 2,
                              child: const CircleAvatar(
                                radius: 13,
                                backgroundColor: Colors.white,
                                child: Icon(Icons.edit, size: 16, color: Colors.black),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Angela',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Points & Card Count
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _profileStat('1020', 'Points'),
                  Container(
                    width: 1,
                    height: 28,
                    color: Colors.grey[300],
                    margin: const EdgeInsets.symmetric(horizontal: 18),
                  ),
                  _profileStat('10', 'Card Count'),
                ],
              ),
              const SizedBox(height: 16),
              // Orders, Check-in, Exchange, Balance, Friends
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Card(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _profileMenuIcon(Icons.receipt_long, 'My Orders', gold),
                            _profileMenuIcon(Icons.check_circle_outline, 'Check-in', gold),
                            _profileMenuIcon(Icons.swap_horiz, 'Exchange', gold),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                children: [
                                  const Text('Balance', style: TextStyle(fontSize: 13)),
                                  const SizedBox(height: 4),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Icon(Icons.account_balance_wallet, color: red, size: 18),
                                      const SizedBox(width: 4),
                                      Text('RM300', style: TextStyle(color: red, fontWeight: FontWeight.bold)),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Container(
                              width: 1,
                              height: 32,
                              color: Colors.grey[300],
                            ),
                            Expanded(
                              child: Column(
                                children: [
                                  const Text('Number of friends invited', style: TextStyle(fontSize: 13)),
                                  const SizedBox(height: 4),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      const Icon(Icons.group, color: gold, size: 18),
                                      const SizedBox(width: 4),
                                      Text('42069', style: TextStyle(color: gold, fontWeight: FontWeight.bold)),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              // Invite Friends Banner
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Container(
                  decoration: BoxDecoration(
                    color: red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.all(12),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Invite Friends,\nThere Are Rewards',
                              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(height: 8),
                            ElevatedButton(
                              onPressed: () {}, // ✅ FIXED HERE
                              style: ButtonStyle(
                                backgroundColor: MaterialStatePropertyAll(Colors.amber),
                                foregroundColor: MaterialStatePropertyAll(Colors.black),
                                padding: MaterialStatePropertyAll(EdgeInsets.symmetric(horizontal: 16)),
                                shape: MaterialStatePropertyAll(RoundedRectangleBorder(
                                  borderRadius: BorderRadius.all(Radius.circular(6)),
                                )),
                                elevation: MaterialStatePropertyAll(0),
                              ),
                              child: const Text('Invite Now! 🤑'),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      const SizedBox(
                        width: 60,
                        height: 60,
                        child: Image(
                          image: AssetImage('assets/images/invite_banner.png'), // Replace with your image
                          fit: BoxFit.cover,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              // Grid Menu
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: GridView.count(
                  crossAxisCount: 4,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  mainAxisSpacing: 16,
                  crossAxisSpacing: 8,
                  childAspectRatio: 0.8,
                  children: [
                    _gridMenu(Icons.ondemand_video, 'Tutorial', gold),
                    _gridMenu(Icons.favorite_border, 'My Favourite', gold),
                    _gridMenu(Icons.card_giftcard, 'Voucher', gold),
                    _gridMenu(Icons.emoji_events, 'Lucky Draw\nRecord', gold),
                    _gridMenu(Icons.share, 'My Sharing', gold),
                    _gridMenu(Icons.star_border, 'My Reviews', gold),
                    _gridMenu(Icons.location_on, 'My Address', gold),
                    _gridMenu(Icons.groups, 'Lucky Group\nRecord', gold),
                    _gridMenu(Icons.verified_user, 'Face\nVerification', gold),
                  ],
                ),
              ),
              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }

  Widget _profileStat(String value, String label) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: const TextStyle(
            color: Colors.black54,
            fontSize: 13,
          ),
        ),
      ],
    );
  }

  Widget _profileMenuIcon(IconData icon, String label, Color color) {
    return Column(
      children: [
        CircleAvatar(
          backgroundColor: color.withOpacity(0.15),
          radius: 20,
          child: Icon(icon, color: color, size: 22),
        ),
        const SizedBox(height: 6),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.black87),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _gridMenu(IconData icon, String label, Color color) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        CircleAvatar(
          backgroundColor: color.withOpacity(0.15),
          radius: 20,
          child: Icon(icon, color: color, size: 22),
        ),
        const SizedBox(height: 6),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.black87),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
