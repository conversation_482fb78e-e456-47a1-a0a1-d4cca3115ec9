import 'package:flutter/material.dart';

class CardExchangeButton extends StatefulWidget {
  final VoidCallback? onTap;
  final bool isEnabled;
  final bool showBadge;
  final bool showClaim;

  const CardExchangeButton({
    super.key,
    this.onTap,
    this.isEnabled = true,
    this.showBadge = true,
    this.showClaim = false,
  });

  @override
  State<CardExchangeButton> createState() => _CardExchangeButtonState();
}

class _CardExchangeButtonState extends State<CardExchangeButton>
    with TickerProviderStateMixin {
  late AnimationController _claimController;
  late AnimationController _waveController;
  late Animation<Color?> _colorAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();

    _claimController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    )..repeat(reverse: true);

    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);

    _colorAnimation = ColorTween(
      begin: const Color(0xFFFFBF00),
      end: const Color(0xFFFCDC85),
    ).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _claimController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double buttonWidth = screenWidth * 0.22;
    double buttonHeight = buttonWidth * 0.66;

    return GestureDetector(
      onTapDown: (_) => setState(() => _isPressed = true),
      onTapUp: (_) => setState(() => _isPressed = false),
      onTapCancel: () => setState(() => _isPressed = false),
      onTap: widget.isEnabled ? widget.onTap : null,
      child: Opacity(
        opacity: widget.isEnabled ? 1.0 : 0.5,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            AnimatedBuilder(
              animation: _colorAnimation,
              builder: (context, child) {
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 150),
                  width: buttonWidth,
                  height: buttonHeight,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: _isPressed
                          ? [Colors.orange.shade600, Colors.white]
                          : [_colorAnimation.value!, Colors.white],
                    ),
                    boxShadow: const [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 8,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 4.0),
                        child: Image.asset(
                          'assets/images/card/icons/tngLogo.png',
                          width: 29,
                          height: 29,
                          fit: BoxFit.contain,
                        ),
                      ),
                      const SizedBox(height: 2),
                      const Text(
                        'Card Exchange',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF474747),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),

            if (widget.showBadge)
              Positioned(
                top: -12,
                left: -12,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFFFFBF00), Color(0xFFE97348)],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: const [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 4,
                        offset: Offset(1, 2),
                      ),
                    ],
                  ),
                  child: const Text(
                    'RM8,888',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

            if (widget.showClaim)
              Positioned(
                top: -6,
                right: -6,
                child: ScaleTransition(
                  scale: Tween(begin: 1.0, end: 1.3).animate(
                    CurvedAnimation(parent: _claimController, curve: Curves.easeInOut),
                  ),
                  child: Container(
                    width: 18,
                    height: 18,
                    alignment: Alignment.center,
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color.fromRGBO(234, 11, 15, 1),
                          Color.fromRGBO(246, 201, 201, 1),
                        ],
                      ),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black26,
                          blurRadius: 3,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Text(
                      '!',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}