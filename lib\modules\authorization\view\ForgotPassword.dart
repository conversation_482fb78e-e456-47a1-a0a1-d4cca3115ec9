import 'package:flutter/material.dart';
import 'package:luckymall/modules/authorization/widgets/login_header.dart';
import '../widgets/phone_number_field.dart';
import '../widgets/verification_code_field.dart';
import '../widgets/password_field.dart';
import '../widgets/login_button.dart';
import 'package:luckymall/modules/authorization/view/PasswordLoginView.dart';

class ForgotPasswordView extends StatefulWidget {
  const ForgotPasswordView({Key? key}) : super(key: key);

  @override
  State<ForgotPasswordView> createState() => _ForgotPasswordViewState();
}

class _ForgotPasswordViewState extends State<ForgotPasswordView> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _verificationController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  bool _isLoading = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _verificationController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _onSendCode() {
    // TODO: Implement send verification code logic
    print('Send verification code');
  }

  void _onChangePassword() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const PasswordLoginView()),
    );
    setState(() {
      _isLoading = true;
    });

    // Simulate API call
    Future.delayed(const Duration(seconds: 2), () {
      setState(() {
        _isLoading = false;
      });
      // TODO: Handle success/error
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFFFFFFFF), Color(0xFFFCD255)],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight:
                    MediaQuery.of(context).size.height -
                    MediaQuery.of(context).padding.top,
              ),
              child: IntrinsicHeight(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const LoginHeader(),
                    const SizedBox(height: 20),
                    // Phone Number Field
                    PhoneNumberField(
                      controller: _phoneController,
                      hintText: 'Please enter your phone number',
                    ),
                    const SizedBox(height: 10),
                    // Verification Code Field
                    VerificationCodeField(
                      controller: _verificationController,
                      hintText: 'Please enter your OTP code',
                      onSendCode: _onSendCode,
                    ),
                    const SizedBox(height: 10),
                    // Password Field
                    PasswordField(
                      controller: _passwordController,
                      label: 'Password',
                      hintText: 'Please enter your password',
                    ),
                    const SizedBox(height: 10),
                    // Confirm Password Field
                    PasswordField(
                      controller: _confirmPasswordController,
                      label: 'Confirm Password',
                      hintText: 'Please confirm your password',
                    ),
                    const SizedBox(height: 20),
                    // Change Password Button
                    LoginButton(
                      text: 'Change Password',
                      onPressed: _onChangePassword,
                      isLoading: _isLoading,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
