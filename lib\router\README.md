# Navigation System Documentation

## Overview

This Flutter app now uses **GoRouter** for declarative routing with the following benefits:
- ✅ Deep linking support
- ✅ Type-safe navigation
- ✅ Nested routing with bottom navigation
- ✅ Browser back button support
- ✅ Authentication guards
- ✅ Centralized route management

## Route Structure

### Main Routes (with bottom navigation)
- `/shop` - Shop listing page
- `/card` - Card main page
- `/lucky-draw` - Lucky draw main page
- `/blind-box` - Blind box main page
- `/profile` - User profile page

### Authentication Routes (outside main app)
- `/login` - Password login
- `/register` - User registration
- `/forgot-password` - Password recovery

### Nested Routes

#### Shop Routes
- `/shop/cart` - Shopping cart
- `/shop/detail/:productId` - Product detail page
- `/shop/favourites` - Favourite items

#### Lucky Draw Routes
- `/lucky-draw/prize-reveal` - Prize reveal page
- `/lucky-draw/group-purchase` - Group purchase page
- `/lucky-draw/participation-records` - Participation records
- `/lucky-draw/search` - Product search

#### Blind Box Routes
- `/blind-box/shop` - Blind box shop
- `/blind-box/buy` - Purchase blind box
- `/blind-box/post/:postId` - Post details

#### Card Routes
- `/card/pack` - Card pack page
- `/card/purchase` - Purchase cards
- `/card/physical-cards` - Physical cards

#### Profile Routes (Protected)
- `/profile/my-address` - Address management
- `/profile/add-address` - Add new address
- `/profile/update-address/:addressId` - Update address

## Usage

### Basic Navigation
```dart
import 'package:go_router/go_router.dart';

// Navigate to a route
context.go('/shop');
context.push('/shop/cart');

// Navigate with parameters
context.push('/shop/detail/123');

// Navigate with data
context.push('/blind-box/buy', extra: {
  'isGroupBuy': true,
  'product': productData,
});

// Go back
context.pop();
```

### Using Navigation Helper
```dart
import '../router/navigation_helper.dart';

// Use helper methods for common navigation
NavigationHelper.goToShop(context);
NavigationHelper.goToShopCart(context);
NavigationHelper.goToShopDetail(context, 'product123');
NavigationHelper.goBack(context);
```

### Authentication
```dart
import '../router/auth_guard.dart';

// Login user
AuthService.login();

// Logout user
AuthService.logout();

// Check if logged in
bool isLoggedIn = AuthService.isLoggedIn;
```

## Route Guards

### Global Authentication Guard
- Redirects unauthenticated users to `/login`
- Redirects authenticated users away from login pages

### Profile Guard
- Ensures profile routes require authentication
- Redirects to login if accessing profile while logged out

## Migration Notes

### What Changed
1. **Removed** `lib/app.dart` - replaced with GoRouter
2. **Updated** `lib/main.dart` - now uses `MaterialApp.router`
3. **Created** `lib/app_shell.dart` - handles bottom navigation
4. **Updated** all `Navigator.push()` calls to use `context.go()` or `context.push()`

### Benefits
- **Deep Linking**: URLs like `/lucky-draw/prize-reveal` work automatically
- **Browser Support**: Back button works correctly on web
- **Type Safety**: Compile-time route validation
- **State Management**: Bottom tab states are preserved
- **Authentication**: Built-in route protection

## File Structure
```
lib/router/
├── app_router.dart          # Main router configuration
├── app_shell.dart           # Bottom navigation shell
├── auth_guard.dart          # Authentication guards
├── navigation_helper.dart   # Navigation utility methods
└── README.md               # This documentation
```
