import 'package:flutter/material.dart';
import '../widgets/CardPack/AppBar.dart';
import '../widgets/CardPack/CardGridDisplay.dart';
import '../widgets/CardPack/CategoryBottomNav.dart';
import '../widgets/CardPack/EmptyState.dart';
import '../widgets/CardPack/TopContainer.dart';
import '../model/cardPack/CardItem.dart';
import '../view-Model/cardPack/MockCards.dart';
import '../widgets/CategorySelectionSheet.dart';
import '../widgets/loadings/PhysicalCard/SelectPhysicalCardWrapper.dart';

class CardPackPage extends StatefulWidget {
  const CardPackPage({super.key});

  @override
  State<CardPackPage> createState() => _CardPackPageState();
}

class _CardPackPageState extends State<CardPackPage> {
  bool showArchived = false;
  List<CardItem> allCards = allCardCatalog; // Simulated card list
  String currentMainCategory = 'fantasy'; // Default main category
  String currentSubType= 'royal'; // Default category
  int selectedTab = 0;
  int get totalCollectedQuantity =>
    allCards.where((c) => c.isOwned).fold(0, (sum, c) => sum + c.quantity);

  void _onToggleArchived(bool value) {
    setState(() {
      showArchived = value;
    });
  }

  void _onCategorySelected(String category) {
    setState(() {
      currentSubType = category;
    });
  }

  

  @override
  Widget build(BuildContext context) {
    
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const PreferredSize(
        preferredSize: Size.fromHeight(70),
        child: CardPackAppBar(),
      ),
      body: SafeArea(
        child: Column(
          children: [
            const SizedBox(height: 8),
            TopContainer(
              onPhysicalCardsTap: () => {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (_) => const SelectPhysicalCardWrapper(),
                  ),
                )
              },
              onHistoryTap: () => {},
              onSelectCategory: () => {
                 showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
                  ),
                  builder: (context) => CategorySelectionSheet(
                    onCategorySelected: (String key) {
                      Navigator.pop(context);
                      _onCategorySelected(key);
                    },
                  ),
                ),
              },
              cardCount: totalCollectedQuantity,
              showArchived: showArchived,
              onToggleArchived: _onToggleArchived,
            ),
            const SizedBox(height: 8),
            Expanded(
              child: visibleCards.isEmpty
                  ? const Center(child: EmptyState())
                  : CardGridDisplay(
                      cards: visibleCards,
                      category: currentSubType,
                      showArchived: showArchived,
                    ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: CardCategoryBottomNav(
        selectedCategory: currentSubType,
        onCategorySelected: _onCategorySelected,
      ),
    );
  }

  List<CardItem> get visibleCards {
  final filtered = allCards.where((c) => c.category == currentSubType);
  return showArchived
      ? filtered.toList()
      : filtered.where((c) => c.isOwned).toList();
  }
}


