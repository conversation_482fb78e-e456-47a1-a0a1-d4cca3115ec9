import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import 'auth_guard.dart';

// Main tab views
import '../modules/shop/view/ShoplistView.dart';
import '../modules/card/view/CardPage.dart';
import '../modules/luckydraw/view/MainpageView.dart';
import '../modules/blindbox/view/BlindboxView.dart';
import '../profile/UserProfile.dart' as profile;

// Lucky Draw sub-views
import '../modules/luckydraw/view/PrizeRevealView.dart';
import '../modules/luckydraw/view/GroupPurchaseView.dart';
import '../modules/luckydraw/view/ParticipationRecordsView.dart';
import '../modules/luckydraw/view/ClaimVoucherView.dart';
import '../modules/luckydraw/view/ClaimVoucherNavView.dart';
import '../modules/luckydraw/view/WinningHistoryView.dart';
import '../modules/luckydraw/view/CommunitySharingView.dart';
import '../modules/luckydraw/view/LatestDrawRecordView.dart';
import '../modules/luckydraw/view/ReviewDetailsView.dart';
import '../modules/luckydraw/view/ProductSearchPage.dart';

// Blindbox sub-views
import '../modules/blindbox/view/BlindboxShopView.dart';
import '../modules/blindbox/view/BlindboxBuyView.dart';
import '../modules/blindbox/view/BlindboxGroupBuyView.dart';
import '../modules/blindbox/view/PostView.dart';

// Card sub-views
import '../modules/card/view/CardPackPage.dart';
import '../modules/card/view/PurchaseCardPage.dart';

// Shop sub-views
import '../modules/shop/view/ShopCartView.dart';
import '../modules/shop/view/ShopDetailView.dart';
import '../modules/favourite/view/FavouriteView.dart';

// Profile sub-views
import '../profile/AddAddressView.dart';
import '../profile/AddressView.dart' as address_view;
import '../profile/UpdateAddress.dart';

// Authorization views
import '../modules/authorization/view/PasswordLoginView.dart';
import '../modules/authorization/view/ForgotPassword.dart';
import '../modules/authorization/view/RegisterView.dart';

// App shell for bottom navigation
import '../app_shell.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/shop',
    redirect: authGuard,
    routes: [
      // Authentication routes (outside shell)
      GoRoute(
        path: '/login',
        builder: (context, state) => const PasswordLoginView(),
      ),
      GoRoute(
        path: '/forgot-password',
        builder: (context, state) => const ForgotPasswordView(),
      ),
      GoRoute(
        path: '/register',
        builder: (context, state) => const RegisterView(),
      ),
      
      // Main app shell with bottom navigation
      ShellRoute(
        builder: (context, state, child) => AppShell(child: child),
        routes: [
          // Shop tab and sub-routes
          GoRoute(
            path: '/shop',
            builder: (context, state) => const ShoplistView(),
            routes: [
              GoRoute(
                path: 'cart',
                builder: (context, state) => const ShopCartView(),
              ),
              GoRoute(
                path: 'detail/:productId',
                builder: (context, state) {
                  final productId = state.pathParameters['productId']!;
                  return ShopDetailView(productId: productId);
                },
              ),
              GoRoute(
                path: 'favourites',
                builder: (context, state) => const FavouriteView(),
              ),
            ],
          ),
          
          // Card tab and sub-routes
          GoRoute(
            path: '/card',
            builder: (context, state) => const CardPage(),
            routes: [
              GoRoute(
                path: 'pack',
                builder: (context, state) => const CardPackPage(),
              ),
              GoRoute(
                path: 'purchase',
                builder: (context, state) => const PurchaseCardPage(),
              ),
              GoRoute(
                path: 'physical-cards',
                builder: (context, state) => const CardPackPage(), // Placeholder
              ),
            ],
          ),
          
          // Lucky Draw tab and sub-routes
          GoRoute(
            path: '/lucky-draw',
            builder: (context, state) => const MainpageView(),
            routes: [
              GoRoute(
                path: 'prize-reveal',
                builder: (context, state) => const PrizeRevealView(),
              ),
              GoRoute(
                path: 'group-purchase',
                builder: (context, state) => const GroupPurchaseView(),
              ),
              GoRoute(
                path: 'participation-records',
                builder: (context, state) => const ParticipationRecordsView(),
              ),
              GoRoute(
                path: 'claim-voucher',
                builder: (context, state) => const ClaimVoucherView(),
              ),
              GoRoute(
                path: 'claim-voucher-nav',
                builder: (context, state) => const ClaimVoucherNav(),
              ),
              GoRoute(
                path: 'winning-history',
                builder: (context, state) => const WinningHistoryView(),
              ),
              GoRoute(
                path: 'community-sharing',
                builder: (context, state) => const CommunitySharingView(),
              ),
              GoRoute(
                path: 'draw-records',
                builder: (context, state) => const LatestDrawRecordView(),
              ),
              GoRoute(
                path: 'review-details',
                builder: (context, state) => const ReviewDetailsView(),
              ),
              GoRoute(
                path: 'search',
                builder: (context, state) {
                  final allProducts = state.extra as List<Map<String, dynamic>>? ?? [];
                  return ProductSearchPage(allProducts: allProducts);
                },
              ),
            ],
          ),
          
          // Blind Box tab and sub-routes
          GoRoute(
            path: '/blind-box',
            builder: (context, state) => const BlindboxView(),
            routes: [
              GoRoute(
                path: 'shop',
                builder: (context, state) => const BlindboxShopView(),
              ),
              GoRoute(
                path: 'buy',
                builder: (context, state) {
                  final extra = state.extra as Map<String, dynamic>?;
                  final isGroupBuy = extra?['isGroupBuy'] ?? false;
                  final product = extra?['product'] ?? {};
                  return BlindboxBuyView(
                    isGroupBuy: isGroupBuy,
                    product: product,
                  );
                },
              ),
              GoRoute(
                path: 'group-buy',
                builder: (context, state) => const BlindboxGroupBuyView(),
              ),
              GoRoute(
                path: 'post/:postId',
                builder: (context, state) {
                  final post = state.extra as Map<String, dynamic>? ?? {};
                  return PostView(post: post);
                },
              ),
              GoRoute(
                path: 'search',
                builder: (context, state) => const BlindboxView(), // Placeholder for search
              ),
            ],
          ),
          
          // Profile tab and sub-routes
          GoRoute(
            path: '/profile',
            redirect: profileGuard,
            builder: (context, state) => const profile.UserProfileView(),
            routes: [
              GoRoute(
                path: 'my-address',
                builder: (context, state) => const address_view.MyAddressView(),
              ),
              GoRoute(
                path: 'add-address',
                builder: (context, state) => const AddAddressView(),
              ),
              GoRoute(
                path: 'update-address/:addressId',
                builder: (context, state) {
                  final addressId = state.pathParameters['addressId']!;
                  // For now, pass a dummy address map - this should be fetched based on addressId
                  return UpdateAddressView(address: {'id': addressId});
                },
              ),
            ],
          ),
        ],
      ),
    ],
  );
}
