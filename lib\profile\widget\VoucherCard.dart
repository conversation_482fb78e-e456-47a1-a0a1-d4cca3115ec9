import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../model/VoucherModel.dart' as model; 

class VoucherCard extends StatelessWidget {
  final String amount;
  final String minSpend;
  final String validDate;
  final model.VoucherStatus status;

  const VoucherCard({
    super.key,
    required this.amount,
    required this.minSpend,
    required this.validDate,
    required this.status,
  });

  Color _borderColor() {
    if (status == model.VoucherStatus.active) return const Color(0xFFFBBF00);
    return Colors.grey.shade300;
  }

  Color _backgroundColor() {
    return Colors.white;
  }

  Color _textColor() {
    if (status == model.VoucherStatus.active) return Colors.black;
    return Colors.grey.shade400;
  }

  Color _iconColor() {
    if (status == model.VoucherStatus.active) return const Color(0xFFFBBF00);
    return Colors.grey.shade500;
  }

  Widget? _statusStamp() {
    if (status == model.VoucherStatus.expired) {
      return Positioned(
        right: 40,
        top: 30,
        child: Opacity(
          opacity: 0.8,
          child: SvgPicture.asset(
            'assets/icons/Expired Stamp.svg',
            width: 60,
            height: 60,
          ),
        ),
      );
    } else if (status == model.VoucherStatus.used) {
      return Positioned(
        right: 40,
        top: 30,
        child: Opacity(
          opacity: 0.8,
          child: SvgPicture.asset(
            'assets/icons/Used Stamp.svg',
            width: 60,
            height: 60,
          ),
        ),
      );
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          decoration: BoxDecoration(
            color: _backgroundColor(),
            border: Border.all(color: _borderColor(), width: 1.5),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              // Left section with icon
              Container(
                width: 80,
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      'assets/icons/voucher.svg',
                      color: _iconColor(),
                      width: 40,
                      height: 40,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      "Selected Products",
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: _textColor(),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              // Divider
              Container(
                width: 1,
                height: 60,
                color: _borderColor(),
              ),
              // Right content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "$amount Off",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: _textColor(),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        "Min. Spend $minSpend",
                        style: TextStyle(fontSize: 12, color: _textColor()),
                      ),
                      const SizedBox(height: 6),
                      Text(
                        "Valid Until $validDate",
                        style: TextStyle(
                          fontSize: 10,
                          color: _textColor().withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        if (_statusStamp() != null) _statusStamp()!,
      ],
    );
  }
}
