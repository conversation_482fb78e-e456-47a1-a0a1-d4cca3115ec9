import 'package:flutter/material.dart';

class ProductReview {
  final String userName;
  final double rating;
  final String date;
  final String reviewText;
  final int helpfulCount;

  const ProductReview({
    required this.userName,
    required this.rating,
    required this.date,
    required this.reviewText,
    required this.helpfulCount,
  });
}

class ProductReviewsSection extends StatelessWidget {
  final double rating;
  final List<ProductReview> reviews;
  final VoidCallback? onViewAllPressed;

  const ProductReviewsSection({
    super.key,
    required this.rating,
    required this.reviews,
    this.onViewAllPressed,
  });

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)

    // Clamp the scale factor to reasonable bounds
    scaleFactor = scaleFactor.clamp(0.8, 1.4);

    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 600) {
      return basePadding; // Small devices
    } else if (screenWidth < 900) {
      return basePadding * 1.2; // Medium devices
    } else {
      return basePadding * 1.5; // Large devices
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFFFF8E1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: getResponsivePadding(16, context),
              vertical: getResponsivePadding(12, context),
            ),
            child: Row(
              children: [
                Text(
                  rating.toStringAsFixed(1),
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(20, context),
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                SizedBox(width: getResponsiveFontSize(4, context)),
                Icon(
                  Icons.star,
                  color: Colors.amber,
                  size: getResponsiveFontSize(20, context),
                ),
                SizedBox(width: getResponsiveFontSize(8, context)),
                Text(
                  'Product Ratings & Reviews',
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(15, context),
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                const Spacer(),
                if (onViewAllPressed != null)
                  GestureDetector(
                    onTap: onViewAllPressed,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: getResponsivePadding(14, context),
                        vertical: getResponsivePadding(6, context),
                      ),
                      decoration: BoxDecoration(
                        color: Colors.amber,
                        borderRadius: BorderRadius.circular(
                          getResponsiveFontSize(8, context),
                        ),
                      ),
                      child: Text(
                        'View All',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(13, context),
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          // Reviews
          Container(
            color: Colors.white,
            padding: EdgeInsets.fromLTRB(
              getResponsivePadding(16, context),
              getResponsivePadding(8, context),
              getResponsivePadding(16, context),
              getResponsivePadding(24, context),
            ),
            child: Column(
              children: reviews
                  .map((review) => _buildReviewCard(context, review))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewCard(BuildContext context, ProductReview review) {
    return Container(
      padding: EdgeInsets.all(getResponsivePadding(12, context)),
      margin: EdgeInsets.only(bottom: getResponsivePadding(12, context)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(getResponsiveFontSize(8, context)),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: getResponsiveFontSize(16, context),
                backgroundColor: Colors.grey[300],
                child: Text(
                  review.userName[0],
                  style: TextStyle(
                    fontSize: getResponsiveFontSize(14, context),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(width: getResponsiveFontSize(8, context)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      review.userName,
                      style: TextStyle(
                        fontSize: getResponsiveFontSize(14, context),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: getResponsiveFontSize(14, context),
                        ),
                        SizedBox(width: getResponsiveFontSize(4, context)),
                        Text(
                          review.rating.toString(),
                          style: TextStyle(
                            fontSize: getResponsiveFontSize(12, context),
                            color: Colors.grey[600],
                          ),
                        ),
                        SizedBox(width: getResponsiveFontSize(8, context)),
                        Text(
                          review.date,
                          style: TextStyle(
                            fontSize: getResponsiveFontSize(12, context),
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: getResponsiveFontSize(8, context)),
          Text(
            review.reviewText,
            style: TextStyle(
              fontSize: getResponsiveFontSize(14, context),
              color: Colors.black87,
            ),
          ),
          SizedBox(height: getResponsiveFontSize(8, context)),
          Row(
            children: [
              Icon(
                Icons.thumb_up_outlined,
                size: getResponsiveFontSize(16, context),
                color: Colors.grey,
              ),
              SizedBox(width: getResponsiveFontSize(4, context)),
              Text(
                'Helpful (${review.helpfulCount})',
                style: TextStyle(
                  fontSize: getResponsiveFontSize(12, context),
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
