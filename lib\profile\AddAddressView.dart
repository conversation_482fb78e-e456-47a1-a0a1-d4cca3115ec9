import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'AddressView.dart';
import 'widgets/AddressForm.dart';
import 'widgets/SubmitButton.dart';

class AddAddressView extends StatefulWidget {
  const AddAddressView({Key? key}) : super(key: key);

  @override
  State<AddAddressView> createState() => _AddAddressViewState();
}

class _AddAddressViewState extends State<AddAddressView> {
  bool isDefault = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Add New Address',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w500,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 1,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: [
            const AddressForm(),
            const SizedBox(height: 16),
            Row(
              children: [
                Checkbox(
                  value: isDefault,
                  onChanged: (value) {
                    setState(() {
                      isDefault = value ?? false;
                    });
                  },
                ),
                const Text('Set as Default Address'),
              ],
            ),
            const SizedBox(height: 24),
            SubmitButton(onSubmit: () {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const MyAddressView()),
              );
            }),
          ],
        ),
      ),
      backgroundColor: Colors.white,
    );
  }
}
