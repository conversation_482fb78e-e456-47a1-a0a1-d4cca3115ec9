import 'package:flutter/material.dart';

class BackgroundContainer extends StatelessWidget {
  final Widget child;
  final String imagePath;

  const BackgroundContainer({
    super.key,
    required this.child,
    this.imagePath = 'images/card/backgroundCard/cardWallpaper.png',
  });

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double aspectRatio = 390 / 482;
    final double imageHeight = screenWidth / aspectRatio;

    return Stack(
      children: [
        Container(
          width: screenWidth,
          height: imageHeight,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(imagePath),
              fit: BoxFit.cover,
            ),
          ),
        ),
        // Overlay child above the image
        Container(
          width: screenWidth,
          height: imageHeight,
          child: child,
        ),
      ],
    );
  }
}