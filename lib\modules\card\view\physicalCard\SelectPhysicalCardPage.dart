import 'package:flutter/material.dart';
import '../../model/cardPack/CardItem.dart';
import '../../view-Model/cardPack/MockCards.dart';
import '../../widgets/PhysicalCard/PhysicalCardAppBar.dart';
import '../../widgets/PhysicalCard/PhysicalCategoryNav.dart';
import '../../widgets/PhysicalCard/PhysicalCardRowItem.dart';
import '../../widgets/PhysicalCard/FloatingCategoryButton.dart';
import '../../widgets/PhysicalCard/PhysicalBottomBar.dart';
import '../../widgets/CardPack/EmptyState.dart';
import '../../model/physicalCardOrder/SelectedCardPayLoad.dart';
import '../../widgets/MessagePurchase/PaymentResultPhysicalPage.dart';

class SelectPhysicalCardPage extends StatefulWidget {
  const SelectPhysicalCardPage({super.key});

  @override
  State<SelectPhysicalCardPage> createState() => _SelectPhysicalCardPageState();
}

class _SelectPhysicalCardPageState extends State<SelectPhysicalCardPage> {
  String selectedCategory = 'royal';
  bool groupSelected = false;
  List<CardItem> ownedCards = [];
  Map<String, int> selectedQuantities = {}; // key: cardId, value: quantity

  @override
  void initState() {
    super.initState();
    _loadCardsForCategory(selectedCategory);
  }

  void _loadCardsForCategory(String category) {
    final filtered = allCardCatalog
        .where((card) => card.category == category && card.isOwned)
        .toList();
    setState(() {
      selectedCategory = category;
      groupSelected = false;
      ownedCards = filtered;
      selectedQuantities.clear(); // <-- no default selected
    });
  }

  void _onCategoryChanged(String category) {
    _loadCardsForCategory(category);
  }

  void _onToggleCardSelection(String id, bool selected) {
    setState(() {
      if (selected) {
        selectedQuantities[id] = 1;
      } else {
        selectedQuantities.remove(id);
      }
    });
  }

  void _onUpdateQuantity(String id, int newQuantity) {
    final currentTotal = selectedQuantities.entries.fold<int>(0, (sum, entry) {
      return entry.key == id ? sum : sum + entry.value;
    });

    final newTotal = currentTotal + newQuantity;

    if (newTotal > 10) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Maximum of 10 cards allowed per order."),
          backgroundColor: Colors.redAccent,
        ),
      );
      return;
    }

    setState(() {
      selectedQuantities[id] = newQuantity;
    });
  }

  void _onToggleGroup() {
    setState(() {
      groupSelected = !groupSelected;
      if (groupSelected) {
        for (var card in ownedCards) {
          selectedQuantities[card.id] = 1;
        }
      } else {
        selectedQuantities.clear();
      }
    });
  }

  void _onConfirm() {
    // Count total quantity across all selected cards
    final totalQuantity = selectedQuantities.values.fold(0, (sum, qty) => sum + qty);

    // 1. Limit: max 10 cards per purchase
    if (totalQuantity > 10) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("You can only order up to 10 physical cards per purchase."),
          backgroundColor: Colors.redAccent,
        ),
      );
      return;
    }

    // 2. Block if nothing selected
    if (selectedQuantities.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Please select at least one card."),
          backgroundColor: Colors.redAccent,
        ),
      );
      return;
    }

    // 3. Prepare payload
    final selectedPayloads = selectedQuantities.entries.map((entry) {
      final card = ownedCards.firstWhere((c) => c.id == entry.key);
      return SelectedCardPayload.fromCardItem(card, entry.value);
    }).toList();

    final payload = {
      'selectedCards': selectedPayloads.map((e) => e.toJson()).toList(),
      'totalItems': selectedPayloads.length,
      'generatedAt': DateTime.now().toIso8601String(),
    };

    debugPrint("✅ JSON Payload:\n$payload");

    // Simulate success
    final isSuccess = true;

    Future.delayed(const Duration(seconds: 1), () {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => PaymentResultPhysicalPage(
            isSuccess: isSuccess,
            message: isSuccess
                ? "Your card selection has been submitted successfully."
                : "Something went wrong. Please try again.",
          ),
        ),
      );
    });

    setState(() {
      selectedQuantities.clear();
      groupSelected = false;
    });
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: PhysicalCardAppBar(),
      body: SafeArea(
          child: Stack(
            children: [
              Positioned.fill(
                child: Column(
                  children: [
                    const SizedBox(height: 8),
                    PhysicalCategoryNav(
                      selectedCategory: selectedCategory,
                      onCategorySelected: _onCategoryChanged,
                      selectedCountMap: selectedQuantities,
                      userCards: ownedCards,
                    ),
                    const SizedBox(height: 12),
                    Expanded(
                      child: ownedCards.isEmpty
                          ? const EmptyState()
                          : ListView.builder(
                              padding: const EdgeInsets.only(bottom: 100),
                              itemCount: ownedCards.length,
                              itemBuilder: (context, index) {
                                final card = ownedCards[index];
                                final isSelected = selectedQuantities.containsKey(card.id);
                                final quantity = selectedQuantities[card.id] ?? 1;
                                return PhysicalCardRowItem(
                                  card: card,
                                  isSelected: isSelected,
                                  selectedQuantity: quantity,
                                  onToggle: (val) =>
                                      _onToggleCardSelection(card.id, val),
                                  onQuantityChanged: (q) =>
                                      _onUpdateQuantity(card.id, q),
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ),
              Positioned(
                bottom: 80,
                right: 0,
                child: FloatingCategoryButton(
                  onCategorySelected: _onCategoryChanged,
                ),
              ),
            ],
          ),
        ),
      bottomNavigationBar: PhysicalBottomBar(
        isGroupSelected: groupSelected,
        onToggleGroup: _onToggleGroup,
        totalSelected: selectedQuantities.length,
        onConfirm: _onConfirm,
      ),
    );
  }
}
