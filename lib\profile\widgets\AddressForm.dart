import 'package:flutter/material.dart';

class AddressForm extends StatelessWidget {
  const AddressForm({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Recipient Name*'),
        const SizedBox(height: 4),
        Text<PERSON>ield(
          decoration: _buildInputDecoration('Please enter recipient name'),
        ),
        const SizedBox(height: 16),

        const Text('Recipient Phone Number*'),
        const SizedBox(height: 4),
        TextField(
          decoration: _buildInputDecoration('Please enter recipient phone number'),
          keyboardType: TextInputType.phone,
        ),
        const SizedBox(height: 16),

        const Text('Postal Code*'),
        const SizedBox(height: 4),
        TextField(
          decoration: _buildInputDecoration('Please enter postal code'),
          keyboardType: TextInputType.number,
        ),
        const SizedBox(height: 16),

        const Text('Full Address*'),
        const SizedBox(height: 4),
        TextField(
          maxLines: 4,
          decoration: _buildInputDecoration('Please enter full address'),
        ),
      ],
    );
  }

  InputDecoration _buildInputDecoration(String hint) {
    return InputDecoration(
      hintText: hint,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      fillColor: Colors.grey[200],
      filled: true,
    );
  }
}
