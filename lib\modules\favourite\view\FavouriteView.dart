import 'package:flutter/material.dart';

class FavouriteView extends StatelessWidget {
  const FavouriteView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: const Color(0xFFE5E5E5),
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          centerTitle: true,
          title: const Text(
            'My Favourite',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500),
          ),
          bottom: const TabBar(
            indicatorColor: Color(0xFFFFB300),
            indicatorWeight: 3,
            labelColor: Colors.black,
            unselectedLabelColor: Colors.black54,
            labelStyle: TextStyle(fontWeight: FontWeight.w500),
            tabs: [
              Tab(text: 'Shopping'),
              Tab(text: 'Blind Box'),
            ],
          ),
        ),
        body: const TabBarView(
          children: [
            _EmptyFavourite(),
            _EmptyFavourite(),
          ],
        ),
      ),
    );
  }
}

class _EmptyFavourite extends StatelessWidget {
  const _EmptyFavourite();

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFE5E5E5),
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.favorite_border,
            size: 72,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 12),
          const Text(
            "Oops, nothing here yet.",
            style: TextStyle(
              color: Colors.black54,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }
}