import 'package:flutter/material.dart';
import '../widgets/CardActionBox.dart';
import '../widgets/CircleIconButton.dart';
import '../widgets/BuyNowButton.dart';
import '../widgets/CardExchangeButton.dart';
import '../widgets/InfoDialog/CardInfoDialog.dart';
import '../widgets/loadings/PurchaseCard/PurchaseCardLoader.dart';
import '../widgets/loadings/CardPack/CardPackLoaderWrapper.dart';


class CardPage extends StatelessWidget {
  const CardPage({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Based on Figma image aspect ratio
    const double aspectRatio = 390 / 482;
    const double sideMargin = 24.0;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(50), // Slimmer like Figma
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFFFFD600), Color(0xFFFFC107)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
        ),
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            child: Column(
              children: [
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    // Image section with padding to sit below app bar
                    Padding(
                      padding: const EdgeInsets.only(top: 50), // height of appBar
                      child: AspectRatio(
                        aspectRatio: aspectRatio,
                        child: Container(
                          width: double.infinity,
                          decoration: const BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage(
                                  'images/card/backgroundCard/cardWallpaper.png'),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                    ),
                    
                     // Circle buttons now BELOW the header, over the image
                      Positioned(
                        top: 10 + MediaQuery.of(context).padding.top, 
                        right: 16,
                        child: Row(
                          children: [
                            CircleIconButton(
                              icon: Icons.info_outline,
                              onTap: () {
                                showDialog(
                                  context: context,
                                  builder: (context) => const CardInfoDialog(),
                                );
                              },
                            ),
                            const SizedBox(width: 12),
                            CircleIconButton(
                              icon: Icons.support_agent,
                              onTap: () {},
                            ),
                          ],
                        ),
                      ),

                    // "BUY NOW" half inside image (overlaps bottom)
                    Positioned(
                      top: screenWidth / aspectRatio + 20, // bottom of image
                      left: (screenWidth - 332) / 2,
                      child: SizedBox(
                        width: 332,
                        child: BuyNowButton(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const PurchaseCardLoader(),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 50), // space under BuyNow

                // Transfer + Card Pack buttons
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: sideMargin),
                  child: Row(
                    children: [
                      Expanded(
                        child: CardActionBox(
                          iconAssetPath:
                              'assets/images/card/icons/transferCard.png',
                          label: 'Transfer Cards',
                          onTap: () {},
                        ),
                      ),
                      const SizedBox(width: 20),
                      Expanded(
                        child: CardActionBox(
                          iconAssetPath:
                              'assets/images/card/icons/cardPack.png',
                          label: 'Card Pack',
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (_) => const CardPackLoaderWrapper(),
                              ),
                            );
                          }
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 40),

                Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding: const EdgeInsets.only(right: sideMargin),
                    child: CardExchangeButton(
                      onTap: () {},
                      isEnabled: true,
                      showBadge: true,
                      showClaim: true,
                    ),
                  ),
                ),

                const SizedBox(height: 30),
              ],
            ),
          );
        },
      ),
    );
  }
}
