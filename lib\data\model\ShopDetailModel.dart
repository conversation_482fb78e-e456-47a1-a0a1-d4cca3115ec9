class ShopDetailModel {
  final String id;
  final String title;
  final String description;
  final List<String> imageUrls;
  final double currentPrice;
  final double? originalPrice;
  final int? discountPercentage;
  final double? maxSavings;
  final double rating;
  final int reviewCount;
  final int soldCount;
  final bool isFavorite;
  final bool groupBuyEnable;
  final String variation;
  final List<String> highlights;
  final List<ProductReview> reviews;
  final String categoryId;
  final int stock; // <-- Add this line

  ShopDetailModel({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrls,
    required this.currentPrice,
    this.originalPrice,
    this.discountPercentage,
    this.maxSavings,
    required this.rating,
    required this.reviewCount,
    required this.soldCount,
    required this.isFavorite,
    required this.groupBuyEnable,
    required this.variation,
    required this.highlights,
    required this.reviews,
    required this.categoryId,
    required this.stock, // <-- Add this line
  });

  ShopDetailModel copyWith({
    String? id,
    String? title,
    String? description,
    List<String>? imageUrls,
    double? currentPrice,
    double? originalPrice,
    int? discountPercentage,
    double? maxSavings,
    double? rating,
    int? reviewCount,
    int? soldCount,
    bool? isFavorite,
    bool? groupBuyEnable,
    String? variation,
    List<String>? highlights,
    List<ProductReview>? reviews,
    String? categoryId,
    int? stock, // <-- Add this line
  }) {
    return ShopDetailModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      imageUrls: imageUrls ?? this.imageUrls,
      currentPrice: currentPrice ?? this.currentPrice,
      originalPrice: originalPrice ?? this.originalPrice,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      maxSavings: maxSavings ?? this.maxSavings,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      soldCount: soldCount ?? this.soldCount,
      isFavorite: isFavorite ?? this.isFavorite,
      groupBuyEnable: groupBuyEnable ?? this.groupBuyEnable,
      variation: variation ?? this.variation,
      highlights: highlights ?? this.highlights,
      reviews: reviews ?? this.reviews,
      categoryId: categoryId ?? this.categoryId,
      stock: stock ?? this.stock, // <-- Add this line
    );
  }
}

class ProductReview {
  final String id;
  final String userName;
  final double rating;
  final String comment;
  final String date;
  final String variation;
  final int helpfulCount;

  ProductReview({
    required this.id,
    required this.userName,
    required this.rating,
    required this.comment,
    required this.date,
    required this.variation,
    required this.helpfulCount,
  });
}