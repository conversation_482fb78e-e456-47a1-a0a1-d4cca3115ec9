import 'package:flutter/material.dart';
import '../widgets/ProductReviewsSection.dart';

class BlindboxBuyVM extends ChangeNotifier {
  bool isFavorite = false;
  int currentImageIndex = 0;
  double appBarOpacity = 0.0;
  final ScrollController scrollController = ScrollController();

  final List<String> imageUrls = [];
  final List<ProductReview> reviews = [
    const ProductReview(
      userName: 'Ashley Lim',
      rating: 5.0,
      date: '2025/02/15',
      reviewText:
          'Dapat lebih 10 jenis snack, semua dalam keadaan yang baik. Ada yang saya tak pernah cuba pun! Packaging pun comel. Memang berbaloil untuk harga ni. Confirm repeat order! 😊',
      helpfulCount: 22,
    ),
  ];

  BlindboxBuyVM() {
    scrollController.addListener(_handleScroll);
  }

  void setFavorite(bool value) {
    isFavorite = value;
    notifyListeners();
  }

  void toggleFavorite() {
    isFavorite = !isFavorite;
    notifyListeners();
  }

  void setCurrentImageIndex(int index) {
    currentImageIndex = index;
    notifyListeners();
  }

  void _handleScroll() {
    double offset = scrollController.offset;
    double opacity = (offset / 120).clamp(0, 1);
    if (opacity != appBarOpacity) {
      appBarOpacity = opacity;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    scrollController.removeListener(_handleScroll);
    scrollController.dispose();
    super.dispose();
  }
}
