import 'package:flutter/material.dart';
import '../../../data/model/ShopDetailModel.dart';

class AddToCartSheet extends StatefulWidget {
  final ShopDetailModel product;
  const AddToCartSheet({Key? key, required this.product}) : super(key: key);

  @override
  State<AddToCartSheet> createState() => _AddToCartSheetState();
}

class _AddToCartSheetState extends State<AddToCartSheet> {
  int quantity = 1;
  String selectedColor = "White";
  String selectedSize = "Medium";

  @override
  Widget build(BuildContext context) {
    final product = widget.product;
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 16, right: 16, top: 16,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Top row: image, price, close
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  product.imageUrls[0],
                  width: 80, height: 80, fit: BoxFit.cover,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: SizedBox(
                  height: 80, // Match image height
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center, // Center vertically
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'RM ${product.currentPrice.toStringAsFixed(2)}',
                        style: const TextStyle(
                          color: Color(0xFFE53E3E),
                          fontWeight: FontWeight.bold,
                          fontSize: 22,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Stock: ${product.stock}',
                        style: const TextStyle(color: Colors.grey, fontSize: 16),
                      ),
                    ],
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          ),
          const Divider(height: 24),
          // Colour
          Align(
            alignment: Alignment.centerLeft,
            child: const Text('Colour', style: TextStyle(fontWeight: FontWeight.w600)),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    backgroundColor: Colors.amber[100],
                    side: BorderSide.none,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8), // <-- Adjust this value
                    ),
                  ),
                  onPressed: () => setState(() => selectedColor = "White"),
                  child: const Text("White", style: TextStyle(color: Colors.black)),
                ),
              ),
            ],
          ),
          const Divider(height: 24),
          // Size
          Align(
            alignment: Alignment.centerLeft,
            child: const Text('Size', style: TextStyle(fontWeight: FontWeight.w600)),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    backgroundColor: selectedSize == "Medium" ? Colors.amber[100] : Colors.white,
                    side: const BorderSide(color: Colors.amber),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8), // <-- Adjust this value
                    ),
                  ),
                  onPressed: () => setState(() => selectedSize = "Medium"),
                  child: const Text("Medium", style: TextStyle(color: Colors.black)),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton(
                  style: OutlinedButton.styleFrom(
                    backgroundColor: selectedSize == "Large" ? Colors.amber[100] : Colors.white,
                    side: const BorderSide(color: Colors.amber),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8), // <-- Adjust this value
                    ),
                  ),
                  onPressed: () => setState(() => selectedSize = "Large"),
                  child: const Text("Large", style: TextStyle(color: Colors.black)),
                ),
              ),
            ],
          ),
          const Divider(height: 24),
          // Quantity
          Align(
            alignment: Alignment.centerLeft,
            child: const Text('Quantity', style: TextStyle(fontWeight: FontWeight.w600)),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              IconButton(
                icon: Icon(Icons.remove, color: Colors.amber[800]),
                onPressed: quantity > 1 ? () => setState(() => quantity--) : null,
              ),
              Container(
                width: 40,
                alignment: Alignment.center,
                child: Text('$quantity', style: const TextStyle(fontSize: 16)),
              ),
              IconButton(
                icon: Icon(Icons.add, color: Colors.amber[800]),
                onPressed: () => setState(() => quantity++),
              ),
            ],
          ),
          const SizedBox(height: 24),
          // Add to Cart button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber[700],
                foregroundColor: Colors.black,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                textStyle: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Added to cart')),
                );
              },
              child: const Text('Add to Cart'),
            ),
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}