// lib/profile/view/VoucherView.dart

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../model/VoucherModel.dart' as model;
import '../widget/VoucherCard.dart';
import '../widget/VoucherCardShimmer.dart';

class VoucherView extends StatefulWidget {
  const VoucherView({super.key});

  @override
  State<VoucherView> createState() => _VoucherViewState();
}

class _VoucherViewState extends State<VoucherView> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool isLoading = true;

  List<Map<String, dynamic>> vouchers = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    Future.delayed(const Duration(seconds: 2), () {
      setState(() {
        vouchers = [
          {
            'amount': 'RM10',
            'minSpend': 'RM30',
            'validDate': '20/12/2025',
            'status': model.VoucherStatus.active,
          },
          {
            'amount': 'RM15',
            'minSpend': 'RM50',
            'validDate': '25/12/2025',
            'status': model.VoucherStatus.active,
          },
          {
            'amount': 'RM8',
            'minSpend': 'RM20',
            'validDate': '15/12/2025',
            'status': model.VoucherStatus.used,
          },
          {
            'amount': 'RM5',
            'minSpend': 'RM15',
            'validDate': '10/12/2025',
            'status': model.VoucherStatus.expired,
          },
        ];
        isLoading = false;
      });
    });
  }

  List<Map<String, dynamic>> _filteredVouchers(model.VoucherStatus? status) {
    if (status == null) return vouchers;
    return vouchers.where((v) => v['status'] == status).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildCustomHeader(context),
          Material(
            elevation: 2,
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              labelColor: Colors.black,
              unselectedLabelColor: Colors.grey,
              indicatorColor: const Color(0xFFFBBF00),
              indicatorSize: TabBarIndicatorSize.tab,
              tabs: const [
                Tab(text: "All"),
                Tab(text: "To Be Used"),
                Tab(text: "Used"),
                Tab(text: "Expired"),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildVoucherList(null),
                _buildVoucherList(model.VoucherStatus.active),
                _buildVoucherList(model.VoucherStatus.used),
                _buildVoucherList(model.VoucherStatus.expired),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 40, left: 16, right: 16, bottom: 12),
      color: Colors.white,
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: const Icon(Icons.chevron_left, size: 28, color: Colors.black),
          ),
          const Spacer(),
          const Text("Voucher", style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.black)),
          const Spacer(),
          const Opacity(
            opacity: 0,
            child: Icon(Icons.chevron_left, size: 28),
          ),
        ],
      ),
    );
  }

  Widget _buildVoucherList(model.VoucherStatus? status) {
    final items = _filteredVouchers(status);

    if (isLoading) {
      return ListView.builder(
        itemCount: 3,
        itemBuilder: (context, index) => const VoucherCardShimmer(),
      );
    }

    if (items.isEmpty) {
      return _buildEmptyState(status);
    }

    return ListView.builder(
      itemCount: items.length,
      itemBuilder: (context, index) {
        final v = items[index];
        return VoucherCard(
          amount: v['amount'],
          minSpend: v['minSpend'],
          validDate: v['validDate'],
          status: v['status'],
        );
      },
    );
  }

  Widget _buildEmptyState(model.VoucherStatus? status) {
    String message;
    if (status == model.VoucherStatus.active) {
      message = "No voucher is available";
    } else if (status == model.VoucherStatus.used) {
      message = "No voucher has been used";
    } else if (status == model.VoucherStatus.expired) {
      message = "No voucher has expired";
    } else {
      message = "No vouchers found";
    }

    return Align(
      alignment: const Alignment(0, -0.1),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            'assets/icons/voucher.svg',
            width: 80,
            height: 80,
            color: Colors.grey.shade300,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],
      ),
    );
  }
}
