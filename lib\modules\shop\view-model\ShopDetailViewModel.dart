import 'package:flutter/material.dart';
import '../../../data/model/ShopDetailModel.dart';
import '../../../data/service/ShopDetailService.dart';
import '../../shop/widget/AddToCartSheet.dart';

class ShopDetailViewModel extends ChangeNotifier {
  ShopDetailModel? product;
  bool isLoading = true;
  String? error;
  int currentImageIndex = 0;

  Future<void> loadProductDetail(String productId) async {
    isLoading = true;
    error = null;
    notifyListeners();

    try {
      product = await ShopDetailService.fetchProductDetail(productId);
    } catch (e) {
      error = e.toString();
    }

    isLoading = false;
    notifyListeners();
  }

  void onImageChanged(int index) {
    currentImageIndex = index;
    notifyListeners();
  }

  void toggleFavorite() {
    if (product != null) {
      product = product!.copyWith(isFavorite: !product!.isFavorite);
      notifyListeners();
    }
  }

  void onAddToCart(BuildContext context) {
    if (product == null) return;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (context) => AddToCartSheet(product: product!),
    );
  }

  void onBuyNow(BuildContext context) {
    if (product == null) return;

    // Check if product has variation (e.g., color or size)
    final hasVariation = (product!.variation.isNotEmpty);

    if (hasVariation) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
        ),
        builder: (context) => AddToCartSheet(product: product!),
      );
    } else {
      // Direct to checkout
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Proceeding to checkout'),
          duration: Duration(seconds: 1),
        ),
      );
      // TODO: Implement your checkout navigation here
    }
  }

  void onChatNow(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening chat'),
        duration: Duration(seconds: 1),
      ),
    );
  }
}