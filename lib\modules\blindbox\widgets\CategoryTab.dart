import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CategoryTab extends StatelessWidget {
  final IconData? icon;
  final String? iconAsset;
  final String label;
  final bool isSelected;

  const CategoryTab({
    super.key,
    this.icon,
    this.iconAsset,
    required this.label,
    this.isSelected = false,
  });

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375; // Base width (iPhone SE)
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding; // Small devices
    } else if (screenWidth < 900) {
      return basePadding * 1.2; // Medium devices
    } else {
      return basePadding * 1.5; // Large devices
    }
  }

  // Helper method to get responsive container size
  double getResponsiveContainerSize(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return 60; // Small devices
    } else if (screenWidth < 900) {
      return 70; // Medium devices
    } else {
      return 80; // Large devices
    }
  }

  @override
  Widget build(BuildContext context) {
    final containerSize = getResponsiveContainerSize(context);
    final Color selectedColor = isSelected ? Colors.amber : Colors.white;
    final Color borderColor = Colors.amber;
    final Color iconColor = isSelected ? Colors.white : Colors.black;
    final Color textColor = isSelected ? Colors.black87 : Colors.black;
    final FontWeight textWeight = isSelected
        ? FontWeight.w600
        : FontWeight.w500;

    return Container(
      margin: EdgeInsets.only(
        left: getResponsivePadding(16, context),
        right: getResponsivePadding(8, context),
      ),
      child: Column(
        children: [
          Container(
            width: containerSize,
            height: containerSize,
            decoration: BoxDecoration(
              color: selectedColor,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: borderColor, width: 2),
            ),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: iconAsset != null
                  ? SvgPicture.asset(
                      iconAsset!,
                      colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
                      width: getResponsiveFontSize(28, context),
                      height: getResponsiveFontSize(28, context),
                    )
                  : Icon(
                      icon ?? Icons.category,
                      fill: 1,
                      color: iconColor,
                      size: getResponsiveFontSize(28, context),
                    ),
            ),
          ),
          SizedBox(height: getResponsivePadding(8, context)),
          Text(
            label,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: getResponsiveFontSize(12, context),
              color: textColor,
              fontWeight: textWeight,
            ),
          ),
        ],
      ),
    );
  }
}
