import 'package:flutter/material.dart';
import '../../../data/model/ShoplistModel.dart';
import '../../../data/service/ShoplistService.dart';
import '../view/ShopDetailView.dart';

class ShoplistViewModel extends ChangeNotifier {
  List<ShopCategory> categories = [];
  List<ShopProduct> products = [];
  String selectedCategoryId = 'all';
  bool isLoading = true;
  final TextEditingController searchController = TextEditingController();

  ShoplistViewModel() {
    loadData();
  }

  Future<void> loadData() async {
    isLoading = true;
    notifyListeners();
    try {
      final results = await Future.wait([
        ShopService.fetchCategories(),
        ShopService.fetchProducts(),
      ]);
      categories = results[0] as List<ShopCategory>;
      products = results[1] as List<ShopProduct>;
    } catch (e) {
      // Handle error if needed
    }
    isLoading = false;
    notifyListeners();
  }

  Future<void> onCategoryTap(String categoryId) async {
    selectedCategoryId = categoryId;
    categories = categories.map((cat) =>
      cat.copyWith(isSelected: cat.id == categoryId)).toList();
    notifyListeners();

    products = await ShopService.getProductsByCategory(categoryId);
    notifyListeners();
  }

  Future<void> onSearch() async {
    final query = searchController.text.trim();
    if (query.isEmpty) {
      await loadData();
      return;
    }
    products = await ShopService.searchProducts(query);
    notifyListeners();
  }

  void onProductTap(BuildContext context, ShopProduct product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ShopDetailView(productId: product.id),
      ),
    );
  }

  void onFavoriteTap(BuildContext context, ShopProduct product) {
    final index = products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      products[index] = product.copyWith(isFavorite: !product.isFavorite);
      notifyListeners();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            product.isFavorite
                ? 'Removed from favorites'
                : 'Added to favorites'
          ),
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }
}