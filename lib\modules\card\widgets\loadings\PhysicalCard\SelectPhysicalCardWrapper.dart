import 'package:flutter/material.dart';
import '../../../view/physicalCard/SelectPhysicalCardPage.dart';
import 'SelectPhysicalCardLoader.dart';

class SelectPhysicalCardWrapper extends StatefulWidget {
  const SelectPhysicalCardWrapper({super.key});

  @override
  State<SelectPhysicalCardWrapper> createState() => _SelectPhysicalCardWrapperState();
}

class _SelectPhysicalCardWrapperState extends State<SelectPhysicalCardWrapper> {
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 2), () {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (_) => const SelectPhysicalCardPage()),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return const SelectPhysicalCardLoader();
  }
}
