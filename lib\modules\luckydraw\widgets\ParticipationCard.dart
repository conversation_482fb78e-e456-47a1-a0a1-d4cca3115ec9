import 'package:flutter/material.dart';

enum ParticipationCardState { claimPrize, waiting, notWin, alreadyClaimed }

enum PrizeType { normal, tngVoucher }

class ParticipationCard extends StatelessWidget {
  final String productName;
  final String imageUrl;
  final String date;
  final String time;
  final String batchNumber;
  final String participationPoints;
  final ParticipationCardState state;
  final PrizeType prizeType;
  final VoidCallback? onClaimPrize;
  final VoidCallback? onClaimTngVoucher;
  final VoidCallback? onViewClaimed;

  const ParticipationCard({
    Key? key,
    required this.productName,
    required this.imageUrl,
    required this.date,
    required this.time,
    required this.batchNumber,
    required this.participationPoints,
    required this.state,
    this.prizeType = PrizeType.normal,
    this.onClaimPrize,
    this.onClaimTngVoucher,
    this.onViewClaimed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Responsive sizing
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    // Use a base width for scaling (e.g., 375 for iPhone X)
    double scale(double size) => size * screenWidth / 375;
    double vScale(double size) => size * screenHeight / 812;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: scale(16), vertical: vScale(8)),
      padding: EdgeInsets.all(scale(16)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(scale(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.4),
            blurRadius: scale(8),
            offset: Offset(0, vScale(2)),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product image and details section
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Product Image
              Container(
                width: scale(80),
                height: scale(80),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(scale(8)),
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(scale(8)),
                  child: Image.network(
                    imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[200],
                        child: Icon(
                          Icons.image_not_supported,
                          color: Colors.grey[400],
                          size: scale(30),
                        ),
                      );
                    },
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        color: Colors.grey[200],
                        child: Center(
                          child: SizedBox(
                            width: scale(24),
                            height: scale(24),
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Color(0xFFFFBF00),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              SizedBox(width: scale(12)),

              // Product details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      productName,
                      style: TextStyle(
                        fontSize: scale(12),
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: vScale(8)),

                    // Details in two columns
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              _buildDetailRow('Date:', date, scale),
                              SizedBox(height: vScale(4)),
                              _buildDetailRow('Time:', time, scale),
                              SizedBox(height: vScale(4)),
                              _buildDetailRow(
                                'Batch Number:',
                                batchNumber,
                                scale,
                              ),
                              SizedBox(height: vScale(4)),
                              _buildDetailRow(
                                'Point Participate:',
                                participationPoints,
                                scale,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: vScale(16)),

          // Status section with message and button
          _buildStatusSection(context, scale, vScale),
        ],
      ),
    );
  }

  // Responsive detail row
  Widget _buildDetailRow(
    String label,
    String value,
    double Function(double) scale,
  ) {
    return RichText(
      text: TextSpan(
        text: label,
        style: TextStyle(
          color: Colors.grey,
          fontSize: scale(11),
          fontWeight: FontWeight.w500,
        ),
        children: [
          TextSpan(
            text: ' $value',
            style: TextStyle(
              color: Color.fromARGB(221, 63, 63, 63),
              fontSize: scale(11),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // Responsive status section
  Widget _buildStatusSection(
    BuildContext context,
    double Function(double) scale,
    double Function(double) vScale,
  ) {
    switch (state) {
      case ParticipationCardState.claimPrize:
        if (prizeType == PrizeType.tngVoucher) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(
                  horizontal: scale(12),
                  vertical: vScale(4),
                ),
                decoration: BoxDecoration(
                  color: Color(0xFFB5E0FF),
                  borderRadius: BorderRadius.circular(scale(15)),
                ),
                child: Text(
                  '🎉 Congratulations! You won a TNG Voucher!',
                  style: TextStyle(
                    color: Color(0xFF1100A9),
                    fontSize: scale(12),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(height: vScale(8)),
              Align(
                alignment: Alignment.centerRight,
                child: ElevatedButton(
                  onPressed: onClaimTngVoucher,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xFF00A91F),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(scale(5)),
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: scale(16),
                      vertical: vScale(8),
                    ),
                  ),
                  child: Text(
                    'Claim TNG Voucher',
                    style: TextStyle(
                      fontSize: scale(12),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          );
        } else {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(
                  horizontal: scale(12),
                  vertical: vScale(4),
                ),
                decoration: BoxDecoration(
                  color: Color(0xFFBCFFB5),
                  borderRadius: BorderRadius.circular(scale(15)),
                ),
                child: Text(
                  '🎉 Congratulations! You won this prize!',
                  style: TextStyle(
                    color: Color(0xFF00A91F),
                    fontSize: scale(12),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(height: vScale(8)),
              Align(
                alignment: Alignment.centerRight,
                child: ElevatedButton(
                  onPressed: onClaimPrize,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xFFFFBF00),
                    foregroundColor: Colors.black87,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(scale(5)),
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: scale(16),
                      vertical: vScale(8),
                    ),
                  ),
                  child: Text(
                    'Claim Prize',
                    style: TextStyle(
                      fontSize: scale(12),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          );
        }
      case ParticipationCardState.waiting:
        return Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            horizontal: scale(12),
            vertical: vScale(4),
          ),
          decoration: BoxDecoration(
            color: Color(0xFFB5E0FF),
            borderRadius: BorderRadius.circular(scale(15)),
          ),
          child: Row(
            children: [
              SizedBox(width: scale(8)),
              Text(
                'Waiting for the prize reveal',
                style: TextStyle(
                  color: Color(0xFF1100A9),
                  fontSize: scale(12),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        );
      case ParticipationCardState.notWin:
        return Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(
            horizontal: scale(12),
            vertical: vScale(4),
          ),
          decoration: BoxDecoration(
            color: Color(0xFFFFD8B5),
            borderRadius: BorderRadius.circular(scale(15)),
          ),
          child: Row(
            children: [
              Text(
                'Sorry, you did not win this item, better luck next time!',
                style: TextStyle(
                  color: Color(0xFFFF0000),
                  fontSize: scale(10.5),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        );
      case ParticipationCardState.alreadyClaimed:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                horizontal: scale(12),
                vertical: vScale(4),
              ),
              decoration: BoxDecoration(
                color: Color(0xFFBCFFB5),
                borderRadius: BorderRadius.circular(scale(15)),
              ),
              child: Text(
                'Prize has already been claimed',
                style: TextStyle(
                  color: Color(0xFF00A91F),
                  fontSize: scale(12),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            SizedBox(height: vScale(8)),
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: onViewClaimed,
                style: TextButton.styleFrom(
                  backgroundColor: Color.fromARGB(255, 128, 130, 131),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(scale(5)),
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: scale(12),
                    vertical: vScale(6),
                  ),
                ),
                child: Text(
                  'Claimed',
                  style: TextStyle(
                    fontSize: scale(12),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        );
    }
  }
}
