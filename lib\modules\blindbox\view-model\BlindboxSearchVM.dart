import 'package:flutter/material.dart';

enum SearchFilter { relevance, latest, mostSold, price }

class BlindboxSearchVM extends ChangeNotifier {
  // Text controller
  late TextEditingController searchController;

  // Search state
  String _searchQuery = '';
  String get searchQuery => _searchQuery;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  SearchFilter _selectedFilter = SearchFilter.relevance;
  SearchFilter get selectedFilter => _selectedFilter;

  // Search results
  List<Map<String, dynamic>> _searchResults = [];
  List<Map<String, dynamic>> get searchResults => _searchResults;

  // Sample data - same as BlindboxShopVM but with timestamps for sorting
  final List<Map<String, dynamic>> allProducts = [
    {
      'title': "<PERSON>'s 3D Visible Window Air Fryer",
      'price': 'RM120.00',
      'rating': 4.9,
      'soldCount': 8,
      'maxSavings': 'GROUP BUY',
      'category': 'Group Order',
      'isDiscounted': false,
      'isGroupBuy': true,
      'imageUrl': 'https://picsum.photos/900/900',
      'timestamp': DateTime.now().subtract(Duration(days: 5)),
    },
    {
      'title': 'My <PERSON> Mall Exclusive Snacks Mystery Box',
      'price': 'RM50.00',
      'rating': 4.6,
      'soldCount': 15,
      'maxSavings': 'RM10',
      'category': 'Snacks',
      'isDiscounted': true,
      'imageUrl': 'https://picsum.photos/1000',
      'timestamp': DateTime.now().subtract(Duration(days: 2)),
    },
    {
      'title': 'My Lucky Mall Exclusive Travel Mystery Box',
      'price': 'RM55.00',
      'rating': 4.6,
      'soldCount': 15,
      'category': 'Travel',
      'isDiscounted': false,
      'imageUrl': 'https://picsum.photos/1100',
      'timestamp': DateTime.now().subtract(Duration(days: 1)),
    },
    {
      'title': 'My Lucky Mall Exclusive Health Mystery Box',
      'price': 'RM75.00',
      'rating': 4.6,
      'soldCount': 15,
      'category': 'Health',
      'isDiscounted': false,
      'imageUrl': 'https://picsum.photos/1200',
      'timestamp': DateTime.now().subtract(Duration(days: 3)),
    },
    {
      'title': 'My Lucky Mall Exclusive Skincare Mystery Box',
      'price': 'RM63.00',
      'rating': 4.6,
      'soldCount': 15,
      'maxSavings': 'RM50',
      'category': 'Skincare',
      'isDiscounted': true,
      'imageUrl': 'https://picsum.photos/1300',
      'timestamp': DateTime.now().subtract(Duration(hours: 12)),
    },
    {
      'title': 'My Lucky Mall Exclusive Fashion Mystery Box',
      'price': 'RM80.00',
      'rating': 4.8,
      'soldCount': 20,
      'category': 'Fashion',
      'isDiscounted': false,
      'imageUrl': 'https://picsum.photos/1400',
      'timestamp': DateTime.now().subtract(Duration(days: 4)),
    },
    {
      'title': 'My Lucky Mall Exclusive Electronics Mystery Box',
      'price': 'RM120.00',
      'rating': 4.7,
      'soldCount': 12,
      'category': 'Electronics',
      'isDiscounted': true,
      'imageUrl': 'https://picsum.photos/1500',
      'timestamp': DateTime.now().subtract(Duration(hours: 6)),
    },
    {
      'title': 'My Lucky Mall Exclusive Beauty Mystery Box',
      'price': 'RM45.00',
      'rating': 4.5,
      'soldCount': 25,
      'category': 'Beauty',
      'isDiscounted': false,
      'imageUrl': 'https://picsum.photos/1600',
      'timestamp': DateTime.now().subtract(Duration(days: 7)),
    },
    {
      'title': 'My Lucky Mall Exclusive Gaming Mystery Box',
      'price': 'RM95.00',
      'rating': 4.9,
      'soldCount': 18,
      'category': 'Electronics',
      'isDiscounted': true,
      'imageUrl': 'https://picsum.photos/1700',
      'timestamp': DateTime.now().subtract(Duration(hours: 18)),
    },
  ];

  BlindboxSearchVM({String? initialQuery}) {
    searchController = TextEditingController(text: initialQuery ?? '');
    _searchQuery = initialQuery ?? '';
    _searchResults = [];

    // Listen to text changes
    searchController.addListener(_onSearchTextChanged);

    // Perform initial search if query is provided
    if (initialQuery != null && initialQuery.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        performSearch();
      });
    }
  }

  void _onSearchTextChanged() {
    _searchQuery = searchController.text;
    notifyListeners();
  }

  @override
  void dispose() {
    searchController.removeListener(_onSearchTextChanged);
    searchController.dispose();
    super.dispose();
  }

  void updateSearchQuery(String query) {
    _searchQuery = query;
    searchController.text = query;
    notifyListeners();
  }

  void selectFilter(SearchFilter filter) {
    _selectedFilter = filter;
    _applyFilter();
    notifyListeners();
  }

  Future<void> performSearch() async {
    // Update search query from controller
    _searchQuery = searchController.text;

    if (_searchQuery.trim().isEmpty) {
      _searchResults = [];
      notifyListeners();
      return;
    }

    _isLoading = true;
    notifyListeners();

    // Simulate network delay
    await Future.delayed(const Duration(milliseconds: 800));

    // Filter products based on search query
    _searchResults = allProducts.where((product) {
      return product['title'].toString().toLowerCase().contains(
        _searchQuery.toLowerCase(),
      );
    }).toList();

    _applyFilter();

    _isLoading = false;
    notifyListeners();
  }

  void _applyFilter() {
    switch (_selectedFilter) {
      case SearchFilter.relevance:
        // Sort by best match (for demo, we'll use a combination of title match and rating)
        _searchResults.sort((a, b) {
          int aScore = _calculateRelevanceScore(a);
          int bScore = _calculateRelevanceScore(b);
          return bScore.compareTo(aScore);
        });
        break;

      case SearchFilter.latest:
        _searchResults.sort((a, b) {
          DateTime aTime = a['timestamp'] as DateTime;
          DateTime bTime = b['timestamp'] as DateTime;
          return bTime.compareTo(aTime);
        });
        break;

      case SearchFilter.mostSold:
        _searchResults.sort((a, b) {
          return (b['soldCount'] as int).compareTo(a['soldCount'] as int);
        });
        break;

      case SearchFilter.price:
        _searchResults.sort((a, b) {
          double priceA = _extractPrice(a['price'].toString());
          double priceB = _extractPrice(b['price'].toString());
          return priceA.compareTo(priceB);
        });
        break;
    }
  }

  int _calculateRelevanceScore(Map<String, dynamic> product) {
    String title = product['title'].toString().toLowerCase();
    String query = _searchQuery.toLowerCase();

    int score = 0;

    // Exact match gets highest score
    if (title == query) score += 100;

    // Starts with query gets high score
    if (title.startsWith(query)) score += 50;

    // Contains query gets medium score
    if (title.contains(query)) score += 25;

    // Add rating bonus
    score += ((product['rating'] as double) * 5).round();

    return score;
  }

  double _extractPrice(String priceString) {
    try {
      // Remove 'RM', spaces, and any non-numeric characters except decimal point
      String cleanPrice = priceString
          .replaceAll('RM', '')
          .replaceAll(' ', '')
          .replaceAll(RegExp(r'[^\d.]'), '');

      // Handle empty string
      if (cleanPrice.isEmpty) return 0.0;

      return double.parse(cleanPrice);
    } catch (e) {
      // Return 0 if parsing fails
      return 0.0;
    }
  }

  String getFilterDisplayName(SearchFilter filter) {
    switch (filter) {
      case SearchFilter.relevance:
        return 'Relevance';
      case SearchFilter.latest:
        return 'Latest';
      case SearchFilter.mostSold:
        return 'Most Sold';
      case SearchFilter.price:
        return 'Price';
    }
  }
}
