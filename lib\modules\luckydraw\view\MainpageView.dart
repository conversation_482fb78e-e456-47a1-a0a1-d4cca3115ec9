import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../widgets/LuckyAppBar.dart';
import '../widgets/CharityCounter.dart';
import '../widgets/ReviewCard.dart';
import '../widgets/LatestDrawsWidget.dart';
import '../widgets/CommunitySharingWidget.dart';
import '../widgets/CategorySelectorWidget.dart';
import '../widgets/PointProductCard.dart';
import '../widgets/PointProductDetails.dart';
import '../widgets/SkeletonLoader.dart';
import '../view/ReviewDetailsView.dart';
import '../view/ParticipationRecordsView.dart';
import '../view/GroupPurchaseView.dart';
import '../view/PrizeRevealView.dart';
import '../widgets/WinPopup.dart';
import '../widgets/LosePopup.dart';
import '../widgets/SuccessJoinPopup.dart';
import '../widgets/SuccessJoinConditionPopup.dart';
import '../widgets/FailedJoinPopup.dart';
import 'package:luckymall/modules/blindbox/view/BlindboxShopView.dart';

class MainpageView extends StatefulWidget {
  const MainpageView({Key? key}) : super(key: key);

  @override
  State<MainpageView> createState() => _MainpageViewState();
}

class _MainpageViewState extends State<MainpageView> {
  String selectedCategory = 'ALL';
  int minPointFilter = 0;
  int maxPointFilter = 1000;

  // Loading states for different components
  bool isReviewsLoading = true;
  bool isLatestDrawsLoading = true;
  bool isCommunitySharingLoading = true;
  bool isPointProductsLoading = true;

  // Cache filtered products to avoid recalculation
  List<Map<String, dynamic>> _cachedFilteredProducts = [];
  String _lastFilterKey = '';

  @override
  void initState() {
    super.initState();
    _updateFilteredProducts();
    _simulateLoading();
  }

  // Optimized loading simulation with single timer
  void _simulateLoading() {
    final loadingStages = [
      {'delay': 1500, 'update': () => isReviewsLoading = false},
      {'delay': 2000, 'update': () => isLatestDrawsLoading = false},
      {'delay': 2500, 'update': () => isCommunitySharingLoading = false},
      {'delay': 3000, 'update': () => isPointProductsLoading = false},
    ];

    for (var stage in loadingStages) {
      Future.delayed(Duration(milliseconds: stage['delay'] as int), () {
        if (mounted) {
          setState(() {
            (stage['update'] as Function)();
          });
        }
      });
    }
  }

  // Update cached filtered products only when filters change
  void _updateFilteredProducts() {
    final filterKey = '$selectedCategory-$minPointFilter-$maxPointFilter';
    if (_lastFilterKey != filterKey) {
      _cachedFilteredProducts = allPointProducts.where((product) {
        final categoryMatch =
            selectedCategory == 'ALL' ||
            product['category'] == selectedCategory;
        final pointValue = product['pointValue'] as int;
        final pointMatch =
            pointValue >= minPointFilter && pointValue <= maxPointFilter;
        return categoryMatch && pointMatch;
      }).toList();
      _lastFilterKey = filterKey;
    }
  }

  // Handle refresh functionality
  Future<void> _handleRefresh() async {
    // Reset all loading states to true in single setState
    setState(() {
      isReviewsLoading = true;
      isLatestDrawsLoading = true;
      isCommunitySharingLoading = true;
      isPointProductsLoading = true;
    });

    // Start loading simulation again
    _simulateLoading();

    // Wait for at least 1 second to show the refresh indicator
    await Future.delayed(const Duration(milliseconds: 1000));

    // Show completion feedback
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.refresh, color: Colors.white, size: 20),
              SizedBox(width: 8),
              Text(
                'Content refreshed successfully!',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
              ),
            ],
          ),
          backgroundColor: const Color(0xFFFFBF00),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  // Sample review data
  final List<Map<String, dynamic>> reviewData = const [
    {
      'reviewerName': 'Tony Stark',
      'avatarUrl':
          'https://static.wikia.nocookie.net/marvelcinematicuniverse/images/9/9d/Iron_Man_Infobox.jpg/revision/latest?cb=20240802142023',
      'date': '2025 / 02 / 15',
      'rating': 5,
      'variation': 'White Large',
      'reviewText':
          'Received with perfect condition. Modern and square design make it more space for every dishes. RECOMMEND 👍',
      'imageUrls': [
        'https://img.freepik.com/free-photo/black-friday-elements-assortment_23-2149074075.jpg?semt=ais_hybrid&w=740',
      ],
    },
    {
      'reviewerName': 'Li Mei',
      'avatarUrl': 'https://randomuser.me/api/portraits/women/44.jpg',
      'date': '2025 / 02 / 14',
      'rating': 4,
      'variation': 'Black Medium',
      'reviewText':
          'Great quality product! Fast shipping and excellent customer service. Will definitely buy again.',
      'imageUrls': [
        'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop',
      ],
    },
    {
      'reviewerName': 'John Smith',
      'avatarUrl': 'https://randomuser.me/api/portraits/men/85.jpg',
      'date': '2025 / 02 / 13',
      'rating': 5,
      'variation': 'Blue Small',
      'reviewText':
          'Amazing product! Exceeded my expectations. The build quality is fantastic and it works perfectly.',
      'imageUrls': [
        'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
      ],
    },
    {
      'reviewerName': 'Sarah Johnson',
      'avatarUrl': 'https://randomuser.me/api/portraits/women/68.jpg',
      'date': '2025 / 02 / 12',
      'rating': 4,
      'variation': 'Red Large',
      'reviewText':
          'Very satisfied with this purchase. Good value for money and arrived quickly.',
      'imageUrls': [
        'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop',
      ],
    },
    {
      'reviewerName': 'Wang Lei',
      'avatarUrl': 'https://randomuser.me/api/portraits/men/12.jpg',
      'date': '2025 / 02 / 11',
      'rating': 5,
      'variation': 'Green Medium',
      'reviewText':
          'Perfect! Exactly as described. High quality materials and excellent craftsmanship.',
      'imageUrls': [
        'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=400&fit=crop',
      ],
    },
  ];

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375;
    double scaleH(double value) => value * screenHeight / 812;
    double scaleText(double value) => value * screenWidth / 375;
    return Scaffold(
      appBar: LuckyAppBar(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          LosePopupOverlay.show(context: context);
        },
        child: Icon(Icons.add, size: scaleW(24)),
      ),
      backgroundColor: Colors.white,
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        color: const Color(0xFFFFBF00),
        backgroundColor: Colors.white,
        strokeWidth: 3.0,
        displacement: scaleH(40),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Simplified background section from charity counter to review cards
              Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFFFFD54F), // Light cream at the top
                      Color(0xFFFFFFFF), // White at the bottom
                    ],
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Charity Counter
                    SizedBox(height: scaleH(8)),
                    CharityCounter(fundAmount: 123),
                    SizedBox(height: scaleH(12)),
                    // Latest Reviews
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: scaleW(18)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Latest Reviews',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: scaleText(15),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              context.push('/lucky-draw/review-details');
                            },
                            child: Text(
                              'See more',
                              style: TextStyle(
                                fontSize: scaleText(13),
                                color: Colors.black87,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: scaleH(8)),
                    // Horizontal scrollable review cards
                    SizedBox(
                      height: scaleH(160),
                      child: isReviewsLoading
                          ? ListView.builder(
                              scrollDirection: Axis.horizontal,
                              padding: EdgeInsets.symmetric(
                                horizontal: scaleW(10),
                              ),
                              itemCount: 3,
                              itemBuilder: (context, index) {
                                return const Padding(
                                  padding: EdgeInsets.only(right: 6.0),
                                  child: ReviewCardSkeleton(),
                                );
                              },
                            )
                          : ListView.builder(
                              scrollDirection: Axis.horizontal,
                              padding: EdgeInsets.symmetric(
                                horizontal: scaleW(10),
                              ),
                              itemCount: reviewData.length,
                              itemBuilder: (context, index) {
                                final review = reviewData[index];
                                return Padding(
                                  padding: EdgeInsets.only(
                                    right: index < reviewData.length - 1
                                        ? scaleW(6.0)
                                        : 0,
                                  ),
                                  child: ReviewCard(
                                    reviewerName: review['reviewerName'],
                                    avatarUrl: review['avatarUrl'],
                                    date: review['date'],
                                    rating: review['rating'],
                                    variation: review['variation'],
                                    reviewText: review['reviewText'],
                                    imageUrls: List<String>.from(
                                      review['imageUrls'],
                                    ),
                                  ),
                                );
                              },
                            ),
                    ),
                    SizedBox(height: scaleH(20)),
                  ],
                ),
              ),

              // Four Feature Buttons
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 18.0),
                child: Column(
                  children: [
                    // First row
                    Row(
                      children: [
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              context.push('/blind-box/shop');
                            },
                            child: _buildFeatureButton(
                              title: 'Blind Box\nStore',
                              image: 'assets/images/blind_box.png',
                              backgroundColor: const Color(0xFFd90019),
                              textColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              context.push('/lucky-draw/prize-reveal');
                            },
                            child: _buildFeatureButton(
                              title: 'Prize\nRevealing',
                              image: 'assets/images/prize_reveal.png',
                              backgroundColor: const Color(0xFFffbf00),
                              textColor: Colors.black,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: scaleH(8)),
                    // Second row
                    Row(
                      children: [
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              context.push('/lucky-draw/group-purchase');
                            },
                            child: _buildFeatureButton(
                              title: 'Group\nPurchases',
                              image: 'assets/images/group_purchase.png',
                              backgroundColor: const Color(0xFFffbf00),
                              textColor: Colors.black,
                            ),
                          ),
                        ),
                        SizedBox(width: scaleW(8)),
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              context.push('/lucky-draw/participation-records');
                            },
                            child: _buildFeatureButton(
                              title: 'Participation\nRecords',
                              image: 'assets/images/participation.png',
                              backgroundColor: const Color(0xFFd90019),
                              textColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              SizedBox(height: scaleH(20)),

              // Latest Draws
              Padding(
                padding: EdgeInsets.symmetric(horizontal: scaleW(18)),
                child: isLatestDrawsLoading
                    ? Container(
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFBF00),
                          borderRadius: BorderRadius.circular(scaleW(12)),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Header
                            Padding(
                              padding: EdgeInsets.fromLTRB(
                                scaleW(16),
                                scaleH(16),
                                scaleW(16),
                                0,
                              ),
                              child: Row(
                                children: [
                                  Text(
                                    'Latest Draws',
                                    style: TextStyle(
                                      fontSize: scaleText(13),
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  const Spacer(),
                                  Text(
                                    'See more',
                                    style: TextStyle(
                                      fontSize: scaleText(13),
                                      color: Colors.black87,
                                      decoration: TextDecoration.underline,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Skeleton content
                            Container(
                              margin: EdgeInsets.all(scaleW(16)),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF9F6EE),
                                borderRadius: BorderRadius.circular(scaleW(12)),
                              ),
                              child: SizedBox(
                                height: scaleH(200),
                                child: ListView.builder(
                                  scrollDirection: Axis.horizontal,
                                  padding: EdgeInsets.all(scaleW(16)),
                                  itemCount: 3,
                                  itemBuilder: (context, index) {
                                    return const LatestDrawCardSkeleton();
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    : LatestDrawsWidget(),
              ),
              SizedBox(height: scaleH(20)),

              // Community Sharing
              isCommunitySharingLoading
                  ? Padding(
                      padding: EdgeInsets.symmetric(horizontal: scaleW(18)),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Community Sharing',
                                style: TextStyle(
                                  fontSize: scaleText(15),
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                              Text(
                                'See more',
                                style: TextStyle(
                                  fontSize: scaleText(13),
                                  color: Colors.black87,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: scaleH(12)),
                          SizedBox(
                            height: scaleH(200),
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: 3,
                              itemBuilder: (context, index) {
                                return const CommunitySharingCardSkeleton();
                              },
                            ),
                          ),
                        ],
                      ),
                    )
                  : CommunitySharingWidget(),
              SizedBox(height: scaleH(30)),

              Divider(
                color: Color(0xFFffbf00),
                thickness: scaleH(3),
                indent: scaleW(20),
                endIndent: scaleW(20),
              ),
              SizedBox(height: scaleH(30)),

              Center(
                child: Text(
                  'Point Product',
                  style: TextStyle(
                    fontSize: scaleText(16),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              SizedBox(height: scaleH(20)),

              // Category Selector Widget
              CategorySelectorWidget(
                onCategoryChanged: (category) {
                  setState(() {
                    selectedCategory = category;
                    _updateFilteredProducts();
                  });
                  print('Selected category: $category');
                },
                onPointRangeChanged: (min, max) {
                  setState(() {
                    minPointFilter = min;
                    maxPointFilter = max;
                    _updateFilteredProducts();
                  });
                  print('Point range changed: $min - $max');
                },
              ),

              SizedBox(height: scaleH(16)),

              // Filter Results Info
              if (!isPointProductsLoading)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: scaleW(16)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Showing ${_cachedFilteredProducts.length} product${_cachedFilteredProducts.length != 1 ? 's' : ''}',
                        style: TextStyle(
                          fontSize: scaleText(14),
                          fontWeight: FontWeight.w500,
                          color: Colors.black54,
                        ),
                      ),
                      if (selectedCategory != 'ALL' ||
                          minPointFilter != 0 ||
                          maxPointFilter != 1000)
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              selectedCategory = 'ALL';
                              minPointFilter = 0;
                              maxPointFilter = 1000;
                              _updateFilteredProducts();
                            });
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: scaleW(8),
                              vertical: scaleH(4),
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFFFFBF00).withOpacity(0.2),
                              borderRadius: BorderRadius.circular(scaleW(4)),
                              border: Border.all(
                                color: const Color(0xFFFFBF00),
                              ),
                            ),
                            child: Text(
                              'Clear Filters',
                              style: TextStyle(
                                fontSize: scaleText(12),
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

              SizedBox(height: scaleH(16)),

              // Point Products Grid
              isPointProductsLoading
                  ? const PointProductGridSkeleton()
                  : PointProductGrid(
                      products: _cachedFilteredProducts,
                      onProductTap: (product) {
                        // For now, we'll use a simple navigation - in a real app, you'd pass product data
                        context.push('/lucky-draw/product-details');
                      },
                    ),
            ],
          ),
        ),
      ),
    );
  }

  // Sample point products data
  static const List<Map<String, dynamic>> allPointProducts = [
    {
      'productName':
          'Smarthpone KingPinix 128GB 4GB RAM 6.58inch 48MP Camera 6000mAh Battery',
      'imageUrl':
          'https://image.made-in-china.com/365f3j00eQuCodqJMscr/3-Inch-IPS-Touch-Screen-2000mAh-Android-4G-LTE-Mini-Smartphone-4GB-RAM-128GB-ROM.webp',
      'variation': 'Large Size',
      'pointValue': 50,
      'rating': 4.6,
      'claimsRemaining': 15,
      'currentParticipants': 35,
      'maxParticipants': 50,
      'category': 'Hot Goods',
      'description':
          'Experience culinary excellence with the Tefal & Jamie Oliver Cook\'s Classic All-In-One Pot. This versatile cooking vessel combines innovative design with practical functionality, perfect for creating delicious meals with ease. Featuring premium non-stick coating, heat-resistant handles, and optimal heat distribution for consistent cooking results.',
      'reviews': [
        {
          'reviewerName': 'Sarah Johnson',
          'avatarUrl': 'https://randomuser.me/api/portraits/women/68.jpg',
          'date': '2025 / 01 / 15',
          'rating': 5,
          'variation': 'Large Size',
          'reviewText':
              'Amazing quality! This pot has transformed my cooking experience. The non-stick surface works perfectly and it\'s so easy to clean.',
          'imageUrls': [
            'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop',
          ],
        },
        {
          'reviewerName': 'Michael Chen',
          'avatarUrl': 'https://randomuser.me/api/portraits/men/32.jpg',
          'date': '2025 / 01 / 10',
          'rating': 4,
          'variation': 'Medium Size',
          'reviewText':
              'Great value for money. Perfect size for my family and the heat distribution is excellent.',
          'imageUrls': [
            'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop',
          ],
        },
      ],
    },
    {
      'productName': 'Gaming Mouse Razer DeathAdder V3',
      'imageUrl':
          'https://hnsgsfp.imgix.net/9/images/detailed/109/Razer_Cobra_Pro_Compact_Wireless_Gaming_Mouse_with_Underglow_Lighting_-_Black_(RZ01-04660100-R3)_01.JPG?fit=fill&bg=0FFF&w=1536&h=901&auto=format,compress',
      'pointValue': 50,
      'rating': 4.8,
      'claimsRemaining': 8,
      'currentParticipants': 22,
      'maxParticipants': 30,
      'category': 'Hot Goods',
      'description':
          'Elevate your gaming experience with the Razer DeathAdder V3 Gaming Mouse. Featuring precision-engineered sensor technology with ultra-accurate tracking up to 30,000 DPI, ergonomic design for extended gaming sessions, and customizable RGB lighting. Built for professional gamers and enthusiasts who demand the best performance.',
      'reviews': [
        {
          'reviewerName': 'Alex Rivera',
          'avatarUrl': 'https://randomuser.me/api/portraits/men/15.jpg',
          'date': '2025 / 01 / 20',
          'rating': 5,
          'variation': 'Black',
          'reviewText':
              'This mouse is incredible! The precision and responsiveness are outstanding. Perfect for competitive gaming.',
          'imageUrls': [
            'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
          ],
        },
        {
          'reviewerName': 'Jessica Wong',
          'avatarUrl': 'https://randomuser.me/api/portraits/women/28.jpg',
          'date': '2025 / 01 / 18',
          'rating': 5,
          'variation': 'Black',
          'reviewText':
              'Best gaming mouse I\'ve ever used. The ergonomics are perfect and the sensor is extremely accurate.',
          'imageUrls': [
            'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
          ],
        },
      ],
    },
    {
      'productName': 'TNG eWallet RM10 Voucher',
      'imageUrl':
          'https://www.barbecook.com/cdn/shop/articles/BC-WOO-6006-SF-03-HR_4a57e31f-cea3-450e-8ac6-39ea475821f0.jpg?v=1678958409',
      'pointValue': 100,
      'rating': 4.9,
      'claimsRemaining': 50,
      'currentParticipants': 8,
      'maxParticipants': 25,
      'category': 'TNG Vouchers',
      'description':
          'Get instant value with this TNG eWallet RM10 Voucher. Perfect for everyday purchases, online shopping, or dining out. Easy to redeem and can be used at thousands of merchants nationwide. No expiry date and instant activation upon redemption.',
      'reviews': [
        {
          'reviewerName': 'David Tan',
          'avatarUrl': 'https://randomuser.me/api/portraits/men/45.jpg',
          'date': '2025 / 01 / 25',
          'rating': 5,
          'variation': 'Digital Voucher',
          'reviewText':
              'Super convenient! Received the voucher instantly and used it immediately. Great value!',
          'imageUrls': [],
        },
        {
          'reviewerName': 'Lisa Kumar',
          'avatarUrl': 'https://randomuser.me/api/portraits/women/38.jpg',
          'date': '2025 / 01 / 22',
          'rating': 5,
          'variation': 'Digital Voucher',
          'reviewText':
              'Perfect for small purchases. The redemption process is very smooth and quick.',
          'imageUrls': [],
        },
      ],
    },
    {
      'productName': 'TNG eWallet RM20 Voucher',
      'imageUrl':
          'https://images.getbats.com/item/ecget/20230613_9e4c1c2b6aff4a43f3c00c8619e7c6ef.png',
      'pointValue': 180,
      'rating': 4.9,
      'claimsRemaining': 25,
      'currentParticipants': 18,
      'maxParticipants': 20,
      'category': 'TNG Vouchers',
      'description':
          'Double the value with this TNG eWallet RM20 Voucher. Ideal for larger purchases, meal deliveries, or shopping sprees. Accepted at all TNG partner merchants including restaurants, retail stores, and online platforms. Easy redemption with instant activation.',
      'reviews': [
        {
          'reviewerName': 'Ahmad Hassan',
          'avatarUrl': 'https://randomuser.me/api/portraits/men/22.jpg',
          'date': '2025 / 01 / 23',
          'rating': 5,
          'variation': 'Digital Voucher',
          'reviewText':
              'Excellent value! Used it for my lunch orders and it worked perfectly. Will definitely get more.',
          'imageUrls': [],
        },
      ],
    },
    {
      'productName': 'Wireless Bluetooth Headphones',
      'imageUrl':
          'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
      'pointValue': 75,
      'rating': 4.5,
      'claimsRemaining': 12,
      'currentParticipants': 15,
      'maxParticipants': 40,
      'category': 'Hot Goods',
      'description':
          'Immerse yourself in superior sound quality with these Wireless Bluetooth Headphones. Featuring active noise cancellation, 30-hour battery life, and premium audio drivers for crystal-clear sound. Perfect for music lovers, travelers, and professionals who demand excellent audio performance.',
      'reviews': [
        {
          'reviewerName': 'Emily Parker',
          'avatarUrl': 'https://randomuser.me/api/portraits/women/55.jpg',
          'date': '2025 / 01 / 19',
          'rating': 4,
          'variation': 'Black',
          'reviewText':
              'Great sound quality and very comfortable to wear. The battery life is impressive, lasting almost the full day.',
          'imageUrls': [
            'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
          ],
        },
        {
          'reviewerName': 'Robert Kim',
          'avatarUrl': 'https://randomuser.me/api/portraits/men/67.jpg',
          'date': '2025 / 01 / 16',
          'rating': 5,
          'variation': 'Black',
          'reviewText':
              'Excellent noise cancellation! Perfect for my daily commute and work from home sessions.',
          'imageUrls': [
            'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
          ],
        },
      ],
    },
    {
      'productName': 'TNG eWallet RM50 Voucher',
      'imageUrl':
          'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=400&fit=crop',
      'pointValue': 450,
      'rating': 5.0,
      'claimsRemaining': 5,
      'currentParticipants': 45,
      'maxParticipants': 50,
      'category': 'TNG Vouchers',
      'description':
          'Maximum value with this TNG eWallet RM50 Voucher. Perfect for big purchases, family dining, or special occasions. Accepted at premium restaurants, department stores, and luxury retailers. Premium voucher with instant activation and no restrictions on usage.',
      'reviews': [
        {
          'reviewerName': 'Maria Santos',
          'avatarUrl': 'https://randomuser.me/api/portraits/women/42.jpg',
          'date': '2025 / 01 / 24',
          'rating': 5,
          'variation': 'Digital Voucher',
          'reviewText':
              'Amazing value! Used it for a family dinner and still had some left over. Highly recommend!',
          'imageUrls': [],
        },
        {
          'reviewerName': 'James Wilson',
          'avatarUrl': 'https://randomuser.me/api/portraits/men/38.jpg',
          'date': '2025 / 01 / 21',
          'rating': 5,
          'variation': 'Digital Voucher',
          'reviewText':
              'Best voucher deal ever! The redemption was instant and I could use it immediately for online shopping.',
          'imageUrls': [],
        },
      ],
    },
  ];

  Widget _buildFeatureButton({
    required String title,
    required String image,
    required Color backgroundColor,
    required Color textColor,
  }) {
    return Container(
      height: 150,
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(image, height: 75, width: 75, fit: BoxFit.contain),
          const SizedBox(height: 8),
          Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }
}
