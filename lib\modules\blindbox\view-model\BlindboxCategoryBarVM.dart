import 'package:flutter/material.dart';

class BlindboxCategory {
  final String id;
  final String name;
  final String? iconAsset;
  final IconData? icon;
  final bool isSelected;

  const BlindboxCategory({
    required this.id,
    required this.name,
    this.iconAsset,
    this.icon,
    this.isSelected = false,
  });

  BlindboxCategory copyWith({bool? isSelected}) {
    return BlindboxCategory(
      id: id,
      name: name,
      iconAsset: iconAsset,
      icon: icon,
      isSelected: isSelected ?? this.isSelected,
    );
  }
}

class BlindboxCategoryBarVM extends ChangeNotifier {
  List<BlindboxCategory> categories = [
    const BlindboxCategory(
      id: 'all',
      name: 'All',
      icon: Icons.grid_view,
      isSelected: true,
    ),
    const BlindboxCategory(
      id: 'group',
      name: 'Group\nOrder',
      iconAsset: 'assets/icons/shop/Group Order.svg',
    ),
    const BlindboxCategory(
      id: 'travel',
      name: 'Travel',
      iconAsset: 'assets/icons/shop/Travel.svg',
    ),
    const BlindboxCategory(
      id: 'stationery',
      name: 'Stationery',
      iconAsset: 'assets/icons/shop/Stationery.svg',
    ),
    const BlindboxCategory(
      id: 'sports',
      name: 'Sports',
      iconAsset: 'assets/icons/shop/Sports.svg',
    ),
    const BlindboxCategory(
      id: 'snacks',
      name: 'Snacks',
      iconAsset: 'assets/icons/shop/Snacks.svg',
    ),
    const BlindboxCategory(
      id: 'pet',
      name: 'Pet',
      iconAsset: 'assets/icons/shop/Pet.svg',
    ),
    const BlindboxCategory(
      id: 'kitchen',
      name: 'Kitchen',
      iconAsset: 'assets/icons/shop/Kitchen.svg',
    ),
    const BlindboxCategory(
      id: 'household',
      name: 'Household',
      iconAsset: 'assets/icons/shop/Household.svg',
    ),
    const BlindboxCategory(
      id: 'health',
      name: 'Health',
      iconAsset: 'assets/icons/shop/Health.svg',
    ),
    const BlindboxCategory(
      id: 'fashion',
      name: 'Fashion',
      iconAsset: 'assets/icons/shop/Fashion.svg',
    ),
    const BlindboxCategory(
      id: 'electronic_device',
      name: 'Electronic\nDevice',
      iconAsset: 'assets/icons/shop/Electronic Device.svg',
    ),
    const BlindboxCategory(
      id: 'electronic_accessories',
      name: 'Electronic\nAccessories',
      iconAsset: 'assets/icons/shop/Electronic Accessories.svg',
    ),
    const BlindboxCategory(
      id: 'beauty',
      name: 'Beauty',
      iconAsset: 'assets/icons/shop/Beauty.svg',
    ),
  ];

  String selectedCategoryId = 'all';

  void selectCategory(String id) {
    selectedCategoryId = id;
    categories = categories
        .map((cat) => cat.copyWith(isSelected: cat.id == id))
        .toList();
    notifyListeners();
  }
}
