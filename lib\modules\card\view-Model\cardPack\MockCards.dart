import '../../model/cardPack/CardItem.dart';

final List<CardItem> allCardCatalog = [
  // Royal
  CardItem(
    id: 'king',
    name: 'King',
    category: 'royal',
    imagePath: 'assets/images/card/cardType/fantasy/royals/king.png',
    quantity: 1,
    isOwned: true,
  ),
  CardItem(
    id: 'queen',
    name: 'Queen',
    category: 'royal',
    imagePath: 'assets/images/card/cardType/fantasy/royals/queen.png',
    quantity: 0,
    isOwned: false,
  ),
  // Knight
  CardItem(
    id: 'ColdSweatKnight',
    name: 'Cold Sweat Knight',
    category: 'knight',
    imagePath: 'assets/images/card/cardType/fantasy/knight/ColdSweatKnight.png',
    quantity: 2,
    isOwned: true,
  ),
  CardItem(
    id: 'CoolKnight',
    name: 'Cool Knight',
    category: 'knight',
    imagePath: 'assets/images/card/cardType/fantasy/knight/CoolKnight.png',
    quantity: 1,
    isOwned: true,
  ),
  CardItem(
    id: 'Crying<PERSON><PERSON>',
    name: 'Crying Knight',
    category: 'knight',
    imagePath: 'assets/images/card/cardType/fantasy/knight/CryingKnight.png',
    quantity: 0,
    isOwned: false,
  ),
  CardItem(
    id: 'HappyKnight',
    name: 'Happy Knight',
    category: 'knight',
    imagePath: 'assets/images/card/cardType/fantasy/knight/HappyKnight.png',
    quantity: 0,
    isOwned: false,
  ),
  CardItem(
    id: 'DizzyKnight',
    name: 'Dizzy Knight',
    category: 'knight',
    imagePath: 'assets/images/card/cardType/fantasy/knight/DizzyKnight.png',
    quantity: 0,
    isOwned: false,
  ),
  CardItem(
    id: 'LaughingKnight',
    name: 'Laughing Knight',
    category: 'knight',
    imagePath: 'assets/images/card/cardType/fantasy/knight/LaughingKnight.png',
    quantity: 0,
    isOwned: false,
  ),
  CardItem(
    id: 'LovelyKnight',
    name: 'Lovely Knight',
    category: 'knight',
    imagePath: 'assets/images/card/cardType/fantasy/knight/LovelyKnight.png',
    quantity: 0,
    isOwned: false,
  ),
  CardItem(
    id: 'PoutingKnight',
    name: 'Pouting Knight',
    category: 'knight',
    imagePath: 'assets/images/card/cardType/fantasy/knight/PoutingKnight.png',
    quantity: 0,
    isOwned: false,
  ),
  CardItem(
    id: 'ScaredKnight',
    name: 'Scared Knight',
    category: 'knight',
    imagePath: 'assets/images/card/cardType/fantasy/knight/ScaredKnight.png',
    quantity: 0,
    isOwned: false,
  ),
  CardItem(
    id: 'ThinkingKnight',
    name: 'Thinking Knight',
    category: 'knight',
    imagePath: 'assets/images/card/cardType/fantasy/knight/ThinkingKnight.png',
    quantity: 0,
    isOwned: false,
  ),
  
  // Wizard
  CardItem(
    id: 'CoolWizard',
    name: 'Cool Wizard',
    category: 'wizard',
    imagePath: 'assets/images/card/cardType/fantasy/knight/CoolWizard.png',
    quantity: 0,
    isOwned: false,
  ),
   CardItem(
    id: 'CryingWizard',
    name: 'Crying Wizard',
    category: 'wizard',
    imagePath: 'assets/images/card/cardType/fantasy/knight/CryingWizard.png',
    quantity: 0,
    isOwned: false,
  ),
   CardItem(
    id: 'DizzyWizard',
    name: 'Dizzy Wizard',
    category: 'wizard',
    imagePath: 'assets/images/card/cardType/fantasy/knight/DizzyWizard.png',
    quantity: 0,
    isOwned: false,
  ),
   CardItem(
    id: 'LovelyWizard',
    name: 'Lovely Wizard',
    category: 'wizard',
    imagePath: 'assets/images/card/cardType/fantasy/knight/LovelyWizard.png',
    quantity: 0,
    isOwned: false,
  ),
];
