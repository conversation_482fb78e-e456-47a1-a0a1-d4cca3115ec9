import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';

class MembersDisplay extends StatelessWidget {
  final List<String> memberAvatars;
  final int totalMembers;
  final int? maxDisplayAvatars;

  const MembersDisplay({
    super.key,
    required this.memberAvatars,
    required this.totalMembers,
    this.maxDisplayAvatars = 4,
  });

  // Helper method to get responsive font size
  double getResponsiveFontSize(double baseSize, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / 375;
    scaleFactor = scaleFactor.clamp(0.8, 1.4);
    return baseSize * scaleFactor;
  }

  // Helper method to get responsive padding
  double getResponsivePadding(double basePadding, BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 600) {
      return basePadding;
    } else if (screenWidth < 900) {
      return basePadding * 1.2;
    } else {
      return basePadding * 1.5;
    }
  }

  @override
  Widget build(BuildContext context) {
    final avatarSize = getResponsiveFontSize(40, context);
    final displayAvatars = memberAvatars.take(maxDisplayAvatars ?? 4).toList();
    final hasMoreMembers = memberAvatars.length > (maxDisplayAvatars ?? 4);

    return Column(
      children: [
        // Avatar row
        Container(
          height: avatarSize,
          child: Stack(
            children: [
              // Display avatars with overlapping effect
              ...displayAvatars.asMap().entries.map((entry) {
                final index = entry.key;
                final avatarUrl = entry.value;
                final leftOffset = index * (avatarSize * 0.75);

                return Positioned(
                  left: leftOffset,
                  child: Container(
                    width: avatarSize,
                    height: avatarSize,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: avatarUrl,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(color: Colors.grey[300]),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: Colors.grey[300],
                          child: Icon(
                            Icons.person,
                            color: Colors.grey[600],
                            size: avatarSize * 0.6,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),

              // Show +N indicator if there are more members
              if (hasMoreMembers)
                Positioned(
                  left: displayAvatars.length * (avatarSize * 0.75),
                  child: Container(
                    width: avatarSize,
                    height: avatarSize,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.grey[400],
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: Center(
                      child: Text(
                        '+${memberAvatars.length - displayAvatars.length}',
                        style: TextStyle(
                          fontSize: getResponsiveFontSize(12, context),
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),

        SizedBox(height: getResponsivePadding(8, context)),

        // Total members count
        Text(
          'Total members: $totalMembers',
          style: TextStyle(
            fontSize: getResponsiveFontSize(16, context),
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }
}
