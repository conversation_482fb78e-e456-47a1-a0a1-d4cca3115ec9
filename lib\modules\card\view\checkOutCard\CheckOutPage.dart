import 'package:flutter/material.dart';
import '../../../../res/utility/BuyCardAPI/apiStub.dart';
import '../../widgets/MessagePurchase/PurchaseResultPage.dart';

class CheckoutPage extends StatefulWidget {
  final int quantity;
  final double total;

  const CheckoutPage({
    super.key,
    required this.quantity,
    required this.total,
  });

  @override
  State<CheckoutPage> createState() => _CheckoutPageState();
}

class _CheckoutPageState extends State<CheckoutPage> {
  String? selectedGateway;
  final TextEditingController nameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController emailController = TextEditingController();

  final paymentGateways = [
    {'name': 'Billplz', 'iconSelected': 'assets/icons/checkOutCard/billPlz2.jpg', 'iconUnselected': 'assets/icons/checkOutCard/billPlz.jpg'},
    {'name': 'Stripe', 'iconSelected': 'assets/icons/checkOutCard/strips2.jpg', 'iconUnselected': 'assets/icons/checkOutCard/strips.jpg'},
    {'name': 'iPay88', 'iconSelected': 'assets/icons/checkOutCard/ipay882.jpg', 'iconUnselected': 'assets/icons/checkOutCard/ipay88.jpg'},
    {'name': 'Lucky Mall', 'iconSelected': 'assets/icons/checkOutCard/lmWallet2.jpg', 'iconUnselected': 'assets/icons/checkOutCard/lmWallet.jpg'},
  ];

  void _selectGateway(String gatewayName) {
    setState(() {
      selectedGateway = gatewayName;
    });
  }

  void _submitOrder() async {
    final request = PaymentRequest(
      gateway: selectedGateway!,
      name: nameController.text,
      phone: phoneController.text,
      email: emailController.text,
      quantity: widget.quantity,
      total: widget.total,
    );

    try {
    // Mocking response from API
    final bool simulateSuccess = true; // 🔁 Change to false to simulate failure
    final Map<String, dynamic> mockResponse = simulateSuccess
    ? {
        'status': 'success',
        'message': 'Card purchase successful!',
      }: {
        'status': 'failed',
        'message': 'Payment could not be completed.',
      };

       
      // Simulate a delay like an API call
      await Future.delayed(const Duration(seconds: 1));

      // Navigate to PurchaseResultPage
      if (!mounted) return;
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (_) => PurchaseResultPage(resultData: mockResponse),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Submission failed: $e')),
      );
    }
  }

  Widget buildFormRow(String label, TextEditingController controller, {String? hint, TextInputType? type}) {
     return Column(
        children: [
          SizedBox(
             height: 48,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Label on the left
                SizedBox(
                  width: 100,
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      label,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                ),

                // Input Field on the right
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: controller,
                    keyboardType: type,
                    decoration: InputDecoration(
                      hintText: hint,
                      hintStyle: const TextStyle(color: Colors.grey),
                      alignLabelWithHint: true,
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(vertical: 12),
                      isCollapsed: true,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          ),
        ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        toolbarHeight: 70,
        elevation: 1,
        shadowColor: Colors.black26,
        backgroundColor: Colors.white,
        title: const Text('Checkout',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.w600, fontSize: 16)),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
            return Container(
              height: constraints.maxHeight,
              color: const Color.fromRGBO(240, 240, 240,1),
              child :SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Total Payment
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 8,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        const Text("Total Payment",
                            style: TextStyle(fontSize: 14, color: Colors.black87)),
                        const SizedBox(height: 4),
                        Text("RM ${widget.total.toStringAsFixed(2)}",
                            style: const TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: Colors.red)),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Payment Method Section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 6,
                          offset: Offset(0, 3),
                        )
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text("Payment Methods",
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                        const SizedBox(height: 12),
                        Row(
                          children: paymentGateways.map((gatewayData) {
                            final gateway = gatewayData as Map<String, dynamic>;
                            final isSelected = selectedGateway == gateway['name'];

                            return Expanded(
                              child: GestureDetector(
                                onTap: () => _selectGateway(gateway['name']),
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 300),
                                  margin: const EdgeInsets.symmetric(horizontal: 4),
                                  padding: const EdgeInsets.fromLTRB(5, 12, 5, 12),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(6),
                                    boxShadow: isSelected
                                        ? [
                                            BoxShadow(
                                              color: Colors.amber.withValues(alpha: 0.5),
                                              blurRadius: 10,
                                              spreadRadius: 1,
                                            )
                                          ]
                                        : [
                                            BoxShadow(
                                              color: Colors.grey.shade200,
                                              blurRadius: 4,
                                            )
                                          ],
                                    border: Border.all(
                                      color: isSelected ? Colors.amber : Colors.grey.shade300,
                                      width: 1.0,
                                    ),
                                  ),
                                  child: Column(
                                    children: [
                                      Image.asset(
                                        isSelected
                                            ? gateway['iconSelected']
                                            : gateway['iconUnselected'],
                                        height: 36,
                                        fit: BoxFit.contain,
                                      ),
                                      const SizedBox(height: 8),
                                      FittedBox(
                                        fit: BoxFit.scaleDown,
                                        child: Text(
                                          gateway['name'],
                                          style: TextStyle(
                                            fontWeight: FontWeight.w600,
                                            color: isSelected ? Colors.black : Colors.grey,
                                            fontSize: 13,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                        const SizedBox(height: 10),
                        const Text(
                          "Refunds are not supported through Billplz.",
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        )
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Billing Info Section
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 6,
                          offset: Offset(0, 3),
                        )
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text("Billing Information",
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                        const SizedBox(height: 12),
                        buildFormRow("Name", nameController, hint: "Enter name..."),
                        buildFormRow("Mobile Number", phoneController, type: TextInputType.phone , hint: "Enter mobile number..."),
                        buildFormRow("Email", emailController, type: TextInputType.emailAddress, hint: "Enter Email..."),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),

      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: const BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              blurRadius: 10,
              color: Colors.black26,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: SizedBox(
            width: double.infinity,
            height: 52,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.amber,
                foregroundColor: Colors.black,
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: selectedGateway == null ? null : _submitOrder,
              child: const Text("Proceed to Pay",
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            ),
          ),
        ),
      ),
    
    );
  }
}
