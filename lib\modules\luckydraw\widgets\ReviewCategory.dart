import 'package:flutter/material.dart';

class ReviewCategory extends StatelessWidget {
  final Function(String)? onCategorySelected;
  final String? initialCategory;

  ReviewCategory({Key? key, this.onCategorySelected, this.initialCategory})
    : super(key: key);

  final List<String> categories = [
    'All',
    'Main Product',
    'Point Product',
    'Blind Box',
    'Lucky Group',
  ];

  @override
  Widget build(BuildContext context) {
    final int initialIndex = initialCategory != null
        ? categories.indexOf(initialCategory!)
        : 0;

    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375; // base width 375
    double scaleH(double value) =>
        value * screenHeight / 812; // base height 812
    double scaleText(double value) => value * screenWidth / 375;

    return DefaultTabController(
      length: categories.length,
      initialIndex: initialIndex >= 0 ? initialIndex : 0,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Material(
            color: Colors.white,
            elevation: 2,
            child: SizedBox(
              height: scaleH(60),
              width: double.infinity,
              child: TabBar(
                isScrollable: true,
                indicatorColor: const Color(0xFFFBBF00),
                labelColor: Colors.black,
                unselectedLabelColor: Colors.grey,
                labelStyle: TextStyle(
                  fontSize: scaleText(14),
                  fontWeight: FontWeight.w600,
                ),
                onTap: (index) {
                  onCategorySelected?.call(categories[index]);
                },
                tabs: categories
                    .map((category) => Tab(text: category))
                    .toList(),
              ),
            ),
          ),
          // You can add a TabBarView here if needed
          // Expanded(
          //   child: TabBarView(
          //     children: categories.map((category) {
          //       return Center(child: Text('Content for $category'));
          //     }).toList(),
          //   ),
          // ),
        ],
      ),
    );
  }
}
