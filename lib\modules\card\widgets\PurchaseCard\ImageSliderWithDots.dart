import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';

class ImageSliderWithDots extends StatelessWidget {
  final List<String> imageUrls;
  final int currentIndex;
  final ValueChanged<int>? onPageChanged;

  const ImageSliderWithDots({
    super.key,
    required this.imageUrls,
    required this.currentIndex,
    required this.onPageChanged,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    const double aspectRatio = 390 / 334;
    final double sliderHeight = screenWidth / aspectRatio;

    return SizedBox(
      width: screenWidth,
      height: sliderHeight,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          CarouselSlider(
            options: CarouselOptions(
              height: sliderHeight,
              autoPlay: true,
              viewportFraction: 1.0,
              onPageChanged: (index, _) => onPageChanged?.call(index),
            ),
            items: imageUrls.map((url) {
              return Image.asset(
                url,
                width: screenWidth,
                fit: BoxFit.cover,
              );
            }).toList(),
          ),
          Positioned(
            bottom: 12,
            child: Row(
              children: imageUrls.asMap().entries.map((entry) {
                final isActive = currentIndex == entry.key;
                return AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  width: isActive ? 12 : 8,
                  height: isActive ? 12 : 8,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    color: isActive ? Colors.amber : Colors.white54,
                    shape: BoxShape.circle,
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
