import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/ProductImageSection.dart';
import '../widgets/ProductInfoSection.dart';
import '../widgets/ProductDescriptionSection.dart';
import '../widgets/ProductReviewsSection.dart';
import '../widgets/ProductOverlayAppBar.dart';
import '../widgets/ProductBottomActionBar.dart';
import '../view-model/BlindboxBuyVM.dart';

class BlindboxBuyView extends StatelessWidget {
  final bool isGroupBuy;
  final Map<String, dynamic> product;
  const BlindboxBuyView({
    super.key,
    this.isGroupBuy = false,
    required this.product,
  });

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => BlindboxBuyVM(),
      child: Consumer<BlindboxBuyVM>(
        builder: (context, vm, _) {
          return Scaffold(
            backgroundColor: Colors.white,
            body: Stack(
              children: [
                SingleChildScrollView(
                  controller: vm.scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Product Image Section
                      ProductImageSection(
                        imageUrls:
                            product['imageUrls'] ??
                            (product['imageUrl'] != null
                                ? [product['imageUrl']]
                                : []),
                        currentImageIndex: vm.currentImageIndex,
                        onImageChanged: vm.setCurrentImageIndex,
                        isFavorite: vm.isFavorite,
                        onFavoritePressed: vm.toggleFavorite,
                      ),
                      // Product Info Section
                      ProductInfoSection(
                        price: product['price']?.toString() ?? '',
                        originalPrice:
                            product['originalPrice']?.toString() ?? '',
                        discountPercentage:
                            product['discountPercentage']?.toString() ?? '',
                        soldCount: product['soldCount'] ?? 0,
                        pointsOffer: product['pointsOffer']?.toString() ?? '',
                        title: product['title']?.toString() ?? '',
                        variation: product['variation']?.toString() ?? '',
                        isFavorite: vm.isFavorite,
                        onFavoritePressed: vm.toggleFavorite,
                      ),
                      const Divider(
                        height: 1,
                        thickness: 8,
                        color: Color(0xFFF5F5F5),
                      ),
                      // Product Description Section
                      ProductDescriptionSection(
                        title: 'Product Description',
                        description: product['description']?.toString() ?? '',
                      ),
                      const Divider(
                        height: 1,
                        thickness: 8,
                        color: Color(0xFFF5F5F5),
                      ),
                      // Product Reviews Section
                      ProductReviewsSection(
                        rating: 4.9,
                        reviews: vm.reviews,
                        onViewAllPressed: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('View all reviews')),
                          );
                        },
                      ),
                    ],
                  ),
                ),
                // Overlay AppBar
                ProductOverlayAppBar(
                  opacity: vm.appBarOpacity,
                  onCartPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Cart pressed')),
                    );
                  },
                  onSharePressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Share pressed')),
                    );
                  },
                  cartItemCount: 1,
                ),
              ],
            ),
            bottomNavigationBar: ProductBottomActionBar(
              type: isGroupBuy
                  ? ProductBottomActionBarType.groupBuy
                  : ProductBottomActionBarType.chatAndBuy,
              onChatPressed: () {
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('Chat Now')));
              },
              onBuyNowPressed: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Buy Now pressed')),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
