import 'package:flutter/material.dart';
import 'modules/luckydraw/view/MainpageView.dart';
import 'modules/luckydraw/view/WinningHistoryView.dart';
import 'modules/luckydraw/view/ClaimVoucherView.dart';
import 'modules/luckydraw/view/PrizeRevealView.dart';
import 'modules/blindbox/view/BlindboxView.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'MY Lucky Mall (Lucky Draw)',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: MainpageView(),
      debugShowCheckedModeBanner: false,
    );
  }
}
