import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CardCategoryBottomNav extends StatelessWidget {
  final String selectedCategory;
  final ValueChanged<String> onCategorySelected;

  const CardCategoryBottomNav({
    super.key,
    required this.selectedCategory,
    required this.onCategorySelected,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Base on screen width, adjust scale for icons and text
    final double iconSize = screenWidth * 0.08; // ~28 on 390px
    final double circleSize = screenWidth * 0.14; // ~54 on 390px
    final double fontSize = screenWidth * 0.027; // ~11 on 390px
    final double navBarHeight = screenWidth * 0.25; // ~96 on 390px
    
    final List<_CategoryItem> categories = [
      _CategoryItem(
        key: 'royal',
        defaultIcon: 'assets/icons/cardCategory/King01.svg',
        activeIcon: 'assets/icons/cardCategory/King02.svg',
      ),
      _CategoryItem(
        key: 'knight',
        defaultIcon: 'assets/icons/cardCategory/Knight01.svg',
        activeIcon: 'assets/icons/cardCategory/Knight02.svg',
      ),
      _CategoryItem(
        key: 'wizard',
        defaultIcon: 'assets/icons/cardCategory/Wizard01.svg',
        activeIcon: 'assets/icons/cardCategory/Wizard02.svg',
      ),
      _CategoryItem(
        key: 'smith',
        defaultIcon: 'assets/icons/cardCategory/Smith01.svg',
        activeIcon: 'assets/icons/cardCategory/Smith02.svg',
      ),
      _CategoryItem(
        key: 'elf',
        defaultIcon: 'assets/icons/cardCategory/Elf01.svg',
        activeIcon: 'assets/icons/cardCategory/Elf02.svg',
      ),
    ];

    return Container(
      height: navBarHeight,
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -3), // 3D effect
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: screenWidth * 0.02,
            vertical: screenWidth * 0.025,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: categories.map((cat) {
              final bool isSelected = cat.key == selectedCategory;
              return GestureDetector(
                onTap: () => onCategorySelected(cat.key),
                behavior: HitTestBehavior.opaque,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: circleSize,
                      height: circleSize,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected ? Colors.amber : Colors.grey.shade300,
                      ),
                      child: Center(
                        child: SvgPicture.asset(
                          isSelected ? cat.activeIcon : cat.defaultIcon,
                          width: iconSize,
                          height: iconSize,
                          colorFilter: ColorFilter.mode(
                            isSelected ? Colors.white : Colors.grey,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(height: screenWidth * 0.01),
                    Text(
                      cat.key[0].toUpperCase() + cat.key.substring(1),
                      style: TextStyle(
                        fontSize: fontSize,
                        color: isSelected ? Colors.amber : Colors.grey,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}

class _CategoryItem {
  final String key;
  final String defaultIcon;
  final String activeIcon;

  _CategoryItem({
    required this.key,
    required this.defaultIcon,
    required this.activeIcon,
  });
}
