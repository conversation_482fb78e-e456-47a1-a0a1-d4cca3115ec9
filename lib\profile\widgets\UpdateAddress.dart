import 'package:flutter/material.dart';

class UpdateAddressForm extends StatelessWidget {
  final TextEditingController nameController;
  final TextEditingController phoneController;
  final TextEditingController postalController;
  final TextEditingController addressController;
  final bool isDefault;
  final ValueChanged<bool> onDefaultChanged;
  final VoidCallback onSubmit;
  final VoidCallback onDelete;

  const UpdateAddressForm({
    super.key,
    required this.nameController,
    required this.phoneController,
    required this.postalController,
    required this.addressController,
    required this.isDefault,
    required this.onDefaultChanged,
    required this.onSubmit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: <PERSON>View(
        children: [
          const Text('Recipient Name*'),
          const Sized<PERSON>ox(height: 4),
          TextField(
            controller: nameController,
            decoration: _inputDecoration(),
          ),
          const SizedBox(height: 16),
          const Text('Recipient Phone Number*'),
          const SizedBox(height: 4),
          TextField(
            controller: phoneController,
            keyboardType: TextInputType.phone,
            decoration: _inputDecoration(),
          ),
          const SizedBox(height: 16),
          const Text('Postal Code*'),
          const SizedBox(height: 4),
          TextField(
            controller: postalController,
            keyboardType: TextInputType.number,
            decoration: _inputDecoration(),
          ),
          const SizedBox(height: 16),
          const Text('Full Address*'),
          const SizedBox(height: 4),
          TextField(
            controller: addressController,
            maxLines: 4,
            decoration: _inputDecoration(),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Radio<bool>(
                value: true,
                groupValue: isDefault,
                onChanged: (value) => onDefaultChanged(value ?? false),
                activeColor: Colors.black,
              ),
              const Text('Set as Default Address'),
            ],
          ),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFFBF00),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: onSubmit,
              child: const Text(
                'Edit',
                style: TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFD90019),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              onPressed: onDelete,
              child: const Text(
                'Delete',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  InputDecoration _inputDecoration() {
    return InputDecoration(
      fillColor: Colors.grey[200],
      filled: true,
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
    );
  }
}
