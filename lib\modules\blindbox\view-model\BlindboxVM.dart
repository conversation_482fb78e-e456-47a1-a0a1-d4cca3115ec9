import 'package:flutter/material.dart';

class BlindboxVM extends ChangeNotifier {
  bool isLoading = true;
  final List<Map<String, dynamic>> posts = [
    {
      "imageUrl": "https://picsum.photos/100",
      "title": "Cute dolls!!!",
      "username": "<PERSON><PERSON>",
      "likeCount": 230,
      "isFavorite": true,
    },
    {
      "imageUrl": "https://picsum.photos/200",
      "title": "Piggy picnic fun 🐷🍉",
      "username": "<PERSON>",
      "likeCount": 180,
      "isFavorite": false,
    },
    {
      "imageUrl": "https://picsum.photos/300",
      "title": "Who wants ice cream?",
      "username": "<PERSON>",
      "likeCount": 312,
      "isFavorite": false,
    },
    {
      "imageUrl": "https://picsum.photos/400",
      "title": "Capsule toy reveal!",
      "username": "<PERSON>",
      "likeCount": 90,
      "isFavorite": false,
    },
    {
      "imageUrl": "https://picsum.photos/500",
      "title": "Doll display goals 😍",
      "username": "<PERSON><PERSON>",
      "likeCount": 450,
      "isFavorite": false,
    },
    {
      "imageUrl": "https://picsum.photos/700",
      "title": "Bear family day out 🧸",
      "username": "Chris",
      "likeCount": 207,
      "isFavorite": false,
    },
    {
      "imageUrl": "https://picsum.photos/800",
      "title": "Tiny kawaii home 🏡",
      "username": "Yuki",
      "likeCount": 390,
      "isFavorite": false,
    },
    {
      "imageUrl": "https://picsum.photos/900",
      "title": "My dream shelf 💭",
      "username": "Hana",
      "likeCount": 298,
      "isFavorite": false,
    },
    {
      "imageUrl": "https://picsum.photos/1000",
      "title": "Unboxing mystery box!",
      "username": "Eli",
      "likeCount": 134,
      "isFavorite": false,
    },
    {
      "imageUrl": "https://picsum.photos/1100",
      "title": "Spa day for dolls 🛁",
      "username": "Rina",
      "likeCount": 265,
      "isFavorite": false,
    },
  ];

  BlindboxVM() {
    _simulateLoading();
  }

  void _simulateLoading() async {
    await Future.delayed(const Duration(milliseconds: 1500));
    isLoading = false;
    notifyListeners();
  }
}
