import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// Navigation helper class with common navigation methods
class NavigationHelper {
  
  /// Navigate to login page
  static void goToLogin(BuildContext context) {
    context.go('/login');
  }
  
  /// Navigate to main shop page
  static void goToShop(BuildContext context) {
    context.go('/shop');
  }
  
  /// Navigate to profile page
  static void goToProfile(BuildContext context) {
    context.go('/profile');
  }
  
  /// Navigate to lucky draw page
  static void goToLuckyDraw(BuildContext context) {
    context.go('/lucky-draw');
  }
  
  /// Navigate to blind box page
  static void goToBlindBox(BuildContext context) {
    context.go('/blind-box');
  }
  
  /// Navigate to card page
  static void goToCard(BuildContext context) {
    context.go('/card');
  }
  
  /// Navigate to shop cart
  static void goToShopCart(BuildContext context) {
    context.push('/shop/cart');
  }
  
  /// Navigate to favourites
  static void goToFavourites(BuildContext context) {
    context.push('/shop/favourites');
  }
  
  /// Navigate to shop product detail
  static void goToShopDetail(BuildContext context, String productId) {
    context.push('/shop/detail/$productId');
  }
  
  /// Navigate to prize reveal
  static void goToPrizeReveal(BuildContext context) {
    context.push('/lucky-draw/prize-reveal');
  }
  
  /// Navigate to group purchase
  static void goToGroupPurchase(BuildContext context) {
    context.push('/lucky-draw/group-purchase');
  }
  
  /// Navigate to participation records
  static void goToParticipationRecords(BuildContext context) {
    context.push('/lucky-draw/participation-records');
  }
  
  /// Navigate to blindbox shop
  static void goToBlindboxShop(BuildContext context) {
    context.push('/blind-box/shop');
  }
  
  /// Navigate to blindbox buy page
  static void goToBlindboxBuy(BuildContext context, {
    required bool isGroupBuy,
    required Map<String, dynamic> product,
  }) {
    context.push('/blind-box/buy', extra: {
      'isGroupBuy': isGroupBuy,
      'product': product,
    });
  }
  
  /// Navigate to card pack page
  static void goToCardPack(BuildContext context) {
    context.push('/card/pack');
  }
  
  /// Navigate to card purchase page
  static void goToCardPurchase(BuildContext context) {
    context.push('/card/purchase');
  }
  
  /// Navigate to my address page
  static void goToMyAddress(BuildContext context) {
    context.push('/profile/my-address');
  }
  
  /// Navigate to add address page
  static void goToAddAddress(BuildContext context) {
    context.push('/profile/add-address');
  }
  
  /// Navigate to update address page
  static void goToUpdateAddress(BuildContext context, String addressId) {
    context.push('/profile/update-address/$addressId');
  }
  
  /// Go back to previous page
  static void goBack(BuildContext context) {
    if (context.canPop()) {
      context.pop();
    }
  }
  
  /// Check if can go back
  static bool canGoBack(BuildContext context) {
    return context.canPop();
  }
}
