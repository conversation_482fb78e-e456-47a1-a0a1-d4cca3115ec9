import 'package:flutter/material.dart';

class CardInfoDialog extends StatefulWidget {
  const CardInfoDialog({super.key});

  @override
  State<CardInfoDialog> createState() => _CardInfoDialogState();
}

class _CardInfoDialogState extends State<CardInfoDialog> {
  final PageController _pageController = PageController();
  int _currentIndex = 0;

  final List<List<String>> _pages = [
    ['Buying cards comes with platform-gifted lucky points. For every card purchased, user will  receive 1 lucky point, with a minimum cost of RM1.', 
    'Collecting all 54 unique cards will be able to let the user won a Grand Prize worth RM 8888.'],
    ['The grand prize will not acknowledge the physical card; only virtual cards will be deemed valid.', 
    'There are a total of 13 cards across 4 categories: <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>, along with 2  hidden cards, making a total of 54 unique cards.'],
    ['Participation will have a probability rate of 1/100 to get each card.', 
    'Cards can be used for product discounts and are able to be used in our main store.'],
    ['Please be advised that the cards purchased by users are physical cards. Users may request the  physical card at their convenience, and we will ensure its delivery to their designated location for  collection. Until such a request is made, the card will remain recorded in the user\'s account as a  virtual card.  ', 
    '** This feature and activity are not affiliated with or sponsored by Apple.'],
  ];

  void _nextPage() {
    if (_currentIndex < _pages.length - 1) {
      _pageController.nextPage(duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
    } else {
      Navigator.of(context).pop(); // close
    }
  }

  void _prevPage() {
    if (_currentIndex > 0) {
      _pageController.previousPage(duration: const Duration(milliseconds: 300), curve: Curves.easeInOut);
    }
  }

   @override
  Widget build(BuildContext context) {
    final isFirst = _currentIndex == 0;
    final isLast = _currentIndex == _pages.length - 1;
    final numberStart = _pages.take(_currentIndex).skip(1).fold(1, (sum, list) => sum + list.length);

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Dialog content box
          Container(
            padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
            ),
            width: double.infinity,
            height: 420,
            child: Column(
              children: [
                // Main PageView
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    itemCount: _pages.length,
                    onPageChanged: (index) {
                      setState(() => _currentIndex = index);
                    },
                    itemBuilder: (context, index) {
                      final rules = _pages[index];
                      final isTitlePage = index == 0;
                      final isFinalPage = index == _pages.length - 1;
                      final startNumber = _pages.take(index).skip(1).fold(1, (sum, list) => sum + list.length);

                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            if (isTitlePage)
                              const Padding(
                                padding: EdgeInsets.only(bottom: 12.0),
                                child: Text(
                                  "What is Card?",
                                  style: TextStyle(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            if (!isFinalPage)
                              ...rules.asMap().entries.map((entry) {
                                final i = entry.key;
                                final text = entry.value;
                                final numberText = isTitlePage ? '' : '${startNumber + i}. ';
                                return Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 6.0),
                                  child: Text(
                                    '$numberText$text',
                                    style: const TextStyle(fontSize: 16),
                                    textAlign: TextAlign.justify,
                                  ),
                                );
                              }).toList()
                            else
                              Column(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(bottom: 8.0),
                                    child: Text(
                                      rules[0],
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.justify,
                                    ),
                                  ),
                                  Text(
                                    rules[1],
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontStyle: FontStyle.italic,
                                    ),
                                    textAlign: TextAlign.justify,
                                  ),
                                ],
                              ),
                          ],
                        ),
                      );
                    },
                  ),
                ),

                const SizedBox(height: 16),

                // Dot indicator
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(_pages.length, (index) {
                    return Container(
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      width: 10,
                      height: 10,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _currentIndex == index ? Colors.amber : Colors.grey.shade400,
                      ),
                    );
                  }),
                ),

                const SizedBox(height: 16),

                // Text buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: _currentIndex > 0 ? _prevPage : null,
                      child: const Text("Back"),
                    ),
                    TextButton(
                      onPressed: _nextPage,
                      child: Text(isLast ? "Close" : "Next"),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Left arrow
          if (_currentIndex > 0)
            Positioned(
              left: -12,
              child: _AnimatedArrowButton(
                icon: Icons.arrow_back_ios_new,
                onTap: _prevPage,
              ),
            ),

          // Right arrow
          if (_currentIndex < _pages.length - 1)
            Positioned(
              right: -12,
              child: _AnimatedArrowButton(
                icon: Icons.arrow_forward_ios,
                onTap: _nextPage,
              ),
            ),
        ],
      ),
    );
  }
}

//  Animated Arrow Button with Scale effect
class _AnimatedArrowButton extends StatefulWidget {
  final IconData icon;
  final VoidCallback onTap;

  const _AnimatedArrowButton({required this.icon, required this.onTap});

  @override
  State<_AnimatedArrowButton> createState() => _AnimatedArrowButtonState();
}

class _AnimatedArrowButtonState extends State<_AnimatedArrowButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnim;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
      lowerBound: 0.9,
      upperBound: 1.0,
    );
    _scaleAnim = CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut);
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  void _handleTap() {
    _scaleController.reverse().then((_) {
      _scaleController.forward();
      widget.onTap();
    });
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _scaleAnim,
      child: Material(
        color: Colors.amber.shade100,
        shape: const CircleBorder(),
        elevation: 4,
        child: InkWell(
          borderRadius: BorderRadius.circular(30),
          onTap: _handleTap,
          child: Container(
            padding: const EdgeInsets.all(10),
            child: Icon(widget.icon, size: 18, color: Colors.black87),
          ),
        ),
      ),
    );
  }
}