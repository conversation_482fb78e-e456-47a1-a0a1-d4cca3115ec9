import 'package:flutter/material.dart';
import '../../../data/model/ShoplistModel.dart';

class ShopCard extends StatelessWidget {
  final ShopProduct product;
  final VoidCallback? onTap;
  final VoidCallback? onFavorite;

  const ShopCard({
    Key? key,
    required this.product,
    this.onTap,
    this.onFavorite,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use LayoutBuilder to get the actual card width
    return LayoutBuilder(
      builder: (context, constraints) {
        final cardWidth = constraints.maxWidth;
        // Use a base width for scaling, e.g., 180 (typical mobile card)
        double scale = cardWidth / 180.0;
        // Clamp the scale to avoid too small/large
        scale = scale.clamp(0.85, 1.5);

        return GestureDetector(
          onTap: onTap,
          child: Card(
            elevation: 0, // Remove shadow for a flat look
            margin: const EdgeInsets.all(0),
            color: Colors.white, // Ensure card is white
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(14 * scale),
              side: BorderSide(
                color: Colors.grey.withOpacity(0.5), // Subtle border
                width: 1.3,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image section with favorite button
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(14 * scale),
                    topRight: Radius.circular(14 * scale),
                  ),
                  child: Container(
                    color: Colors.grey[100],
                    width: double.infinity,
                    height: 110 * scale,
                    child: Stack(
                      children: [
                        Positioned.fill(
                          child: Image.network(
                            product.imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey[200],
                                child: Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey,
                                  size: 40 * scale,
                                ),
                              );
                            },
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Container(
                                color: Colors.grey[100],
                                child: Center(
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2 * scale,
                                    color: Colors.grey,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        Positioned(
                          top: 8 * scale,
                          right: 8 * scale,
                          child: GestureDetector(
                            onTap: onFavorite,
                            child: Container(
                              width: 26 * scale,
                              height: 26 * scale,
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.95),
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.08),
                                    blurRadius: 4 * scale,
                                    offset: Offset(0, 1 * scale),
                                  ),
                                ],
                              ),
                              child: Icon(
                                product.isFavorite
                                    ? Icons.favorite
                                    : Icons.favorite_border,
                                size: 15 * scale,
                                color: product.isFavorite ? Colors.red : Colors.grey[600],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Main content
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 10 * scale,
                      vertical: 10 * scale,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title
                        Text(
                          product.title,
                          style: TextStyle(
                            fontSize: 13 * scale,
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                            height: 1.2,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (product.maxSavings != null) ...[
                          SizedBox(height: 4 * scale),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 6 * scale,
                              vertical: 2 * scale,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.08),
                              borderRadius: BorderRadius.circular(4 * scale),
                              border: Border.all(
                                color: Colors.green.withOpacity(0.18),
                                width: 0.5 * scale,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.local_offer_outlined,
                                  size: 12 * scale,
                                  color: Colors.green[700],
                                ),
                                SizedBox(width: 3 * scale),
                                Flexible(
                                  child: Text(
                                    'Max can save RM${product.maxSavings!.toStringAsFixed(0)}',
                                    style: TextStyle(
                                      fontSize: 10 * scale,
                                      color: Colors.green[800],
                                      fontWeight: FontWeight.w500,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                        SizedBox(height: 6 * scale),
                        // Price and discount
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              'RM${product.currentPrice.toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 15.5 * scale,
                                fontWeight: FontWeight.bold, // Already bold, keep as is
                                color: Colors.red[600],
                              ),
                            ),
                            if (product.discountPercentage != null) ...[
                              SizedBox(width: 6 * scale),
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 5 * scale,
                                  vertical: 2 * scale,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(3 * scale),
                                ),
                                child: Text(
                                  '-${product.discountPercentage}% ',
                                  style: TextStyle(
                                    fontSize: 10 * scale,
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                        if (product.originalPrice != null)
                          Padding(
                            padding: EdgeInsets.only(top: 1 * scale),
                            child: Text(
                              'RM${product.originalPrice!.toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 10 * scale,
                                color: Colors.grey[500],
                                decoration: TextDecoration.lineThrough,
                                decorationColor: Colors.grey[500],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                // Bottom section for rating and sold
                Padding(
                  padding: EdgeInsets.fromLTRB(
                      10 * scale, 0, 10 * scale, 10 * scale),
                  child: Row(
                    children: [
                      Icon(
                        Icons.star,
                        size: 13 * scale,
                        color: Colors.amber[700],
                      ),
                      SizedBox(width: 2 * scale),
                      Text(
                        product.rating.toString(),
                        style: TextStyle(
                          fontSize: 10 * scale,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      if (product.groupBuyEnable) ...[
                        SizedBox(width: 6 * scale),
                        Icon(
                          Icons.groups, // or Icons.group
                          size: 15 * scale,
                          color: Colors.grey[700],
                        ),
                      ],
                      Spacer(),
                      Text(
                        '${product.soldCount} Sold',
                        style: TextStyle(
                          fontSize: 12 * scale,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}