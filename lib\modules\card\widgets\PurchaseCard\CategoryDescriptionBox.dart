import 'package:flutter/material.dart';

class CategoryDescriptionBox extends StatelessWidget {
  final String category;
  const CategoryDescriptionBox({super.key, required this.category});

  @override
  Widget build(BuildContext context) {
    String text = "Card Event - Knights, Wizards, Elves, Blacksmiths, and Hidden Cards appear randomly";
    return Container(
      width: double.infinity,
      color: Colors.black26,
      padding: const EdgeInsets.all(12),
      child: Text(
        text,
        style: const TextStyle(fontSize: 14),
        textAlign: TextAlign.justify,
      ),
    );
  }
}