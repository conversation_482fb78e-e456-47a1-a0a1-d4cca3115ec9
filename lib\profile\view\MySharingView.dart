import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../widgets/PostCard.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'UserPostView.dart';
import 'package:luckymall/profile/widgets/PostCardShimmer.dart';

// Sample posts
final posts = [
  {
    "imageUrl": "https://picsum.photos/220",
    "title": "Cute dolls!!!",
    "likeCount": 230,
  },
  {
    "imageUrl": "https://picsum.photos/200",
    "title": "Piggy picnic fun 🐷🍉",
    "likeCount": 180,
  },
  {
    "imageUrl": "https://picsum.photos/300",
    "title": "Who wants ice cream?",
    "likeCount": 312,
  },
  {
    "imageUrl": "https://picsum.photos/400",
    "title": "Capsule toy reveal!",
    "likeCount": 90,
  },
  {
    "imageUrl": "https://picsum.photos/500",
    "title": "Doll display goals 😍",
    "likeCount": 450,
  },
  {
    "imageUrl": "https://picsum.photos/600",
    "title": "Bear family day out 🧸",
    "likeCount": 207,
  },
  {
    "imageUrl": "https://picsum.photos/700",
    "title": "Tiny kawaii home 🏡",
    "likeCount": 390,
  },
  {
    "imageUrl": "https://picsum.photos/950",
    "title": "My dream shelf 💭",
    "likeCount": 298,
  },
  {
    "imageUrl": "https://picsum.photos/300",
    "title": "Unboxing mystery box!",
    "likeCount": 134,
  },
  {
    "imageUrl": "https://picsum.photos/200",
    "title": "Spa day for dolls 🛁",
    "likeCount": 265,
  },
];

class MySharingView extends StatefulWidget {
  const MySharingView({super.key});

  @override
  State<MySharingView> createState() => _MySharingViewState();
}

class _MySharingViewState extends State<MySharingView> {
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(milliseconds: 1500), () {
      setState(() {
        isLoading = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Yellow background with profile section
            Stack(
              children: [
                Container(
                  width: double.infinity,
                  height: 180,
                  decoration: const BoxDecoration(
                    color: Color(0xFFFFEAAB),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(20),
                    ),
                  ),
                ),
                Positioned(
                  top: 40,
                  left: 16,
                  child: GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.8),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.arrow_back,
                        color: Colors.black,
                        size: 20,
                      ),
                    ),
                  ),
                ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Column(
                    children: [
                      SvgPicture.asset(
                        'assets/icons/Empty Profile.svg',
                        width: 70,
                        height: 70,
                        color: Colors.black,
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        "Angela",
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: 12),
                    ],
                  ),
                ),
              ],
            ),

            // Stats section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Column(
                    children: [
                      const Text(
                        "1020",
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        "Blind Box",
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      const Text(
                        "10",
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        "Card",
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Post Grid or Empty State
            Container(
              padding: const EdgeInsets.fromLTRB(12, 8, 12, 24),
              child: isLoading
                  ? MasonryGridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      mainAxisSpacing: 8,
                      crossAxisSpacing: 8,
                      itemCount: 6,
                      itemBuilder: (context, index) {
                        return const PostCardShimmer();
                      },
                    )
                  : posts.isEmpty
                      ? Padding(
                          padding: const EdgeInsets.symmetric(vertical: 40),
                          child: Center(
                            child: Text(
                              "No post yet 💤",
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[600],
                              ),
                            ),
                          ),
                        )
                      : MasonryGridView.count(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          crossAxisCount: 2,
                          mainAxisSpacing: 8,
                          crossAxisSpacing: 8,
                          itemCount: posts.length,
                          itemBuilder: (context, index) {
                            final post = posts[index];
                            return PostCard(
                              imageUrl: post['imageUrl'] as String,
                              title: post['title'] as String,
                              likeCount: post['likeCount'] as int,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        UserPostView(post: post),
                                  ),
                                );
                              },
                            );
                          },
                        ),
            ),
          ],
        ),
      ),
    );
  }
}
