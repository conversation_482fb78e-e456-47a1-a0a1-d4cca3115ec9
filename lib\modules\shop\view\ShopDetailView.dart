import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-model/ShopDetailViewModel.dart';
import '../widget/ShopDetailReviewCard.dart';
import '../widget/ShopDetailReviewCardSkeleton.dart';
import 'ShopCartView.dart';

class ShopDetailView extends StatelessWidget {
  final String productId;

  const ShopDetailView({Key? key, required this.productId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ShopDetailViewModel()..loadProductDetail(productId),
      child: const _ShopDetailViewBody(),
    );
  }
}

class _ShopDetailViewBody extends StatefulWidget {
  const _ShopDetailViewBody({Key? key}) : super(key: key);

  @override
  State<_ShopDetailViewBody> createState() => _ShopDetailViewBodyState();
}

class _ShopDetailViewBodyState extends State<_ShopDetailViewBody> {
  bool _descExpanded = false;
  final ScrollController _scrollController = ScrollController();
  double _appBarOpacity = 0.0;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_handleScroll);
  }

  void _handleScroll() {
    double offset = _scrollController.offset;
    double opacity = (offset / 120).clamp(0, 1);
    if (opacity != _appBarOpacity) {
      setState(() {
        _appBarOpacity = opacity;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_handleScroll);
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ShopDetailViewModel>(
      builder: (context, vm, child) {
        if (vm.isLoading) {
          // Show skeletons for the whole product detail page while loading
          return Scaffold(
            backgroundColor: Colors.white,
            body: Column(
              children: [
                Expanded(
                  child: ListView(
                    padding: EdgeInsets.zero,
                    children: [
                      // Product image skeleton
                      Container(
                        width: double.infinity,
                        height: MediaQuery.of(context).size.width * 0.75,
                        color: Colors.grey.shade300,
                        child: const Center(
                          child: Icon(Icons.image, color: Colors.white54, size: 64),
                        ),
                      ),
                      // Product info skeleton
                      Padding(
                        padding: const EdgeInsets.fromLTRB(16, 20, 16, 12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Price skeleton
                            Container(
                              width: 120,
                              height: 28,
                              color: Colors.grey.shade300,
                              margin: const EdgeInsets.only(bottom: 12),
                            ),
                            // Discount badges skeleton
                            Row(
                              children: [
                                Container(
                                  width: 100,
                                  height: 20,
                                  color: Colors.grey.shade300,
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  width: 120,
                                  height: 20,
                                  color: Colors.grey.shade300,
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            // Title skeleton
                            Container(
                              width: double.infinity,
                              height: 22,
                              color: Colors.grey.shade300,
                              margin: const EdgeInsets.only(bottom: 8),
                            ),
                            // Variation skeleton
                            Container(
                              width: 160,
                              height: 16,
                              color: Colors.grey.shade300,
                            ),
                          ],
                        ),
                      ),
                      // Product description skeleton
                      Container(
                        color: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.description, color: Colors.orange[600]),
                                const SizedBox(width: 8),
                                Container(
                                  width: 140,
                                  height: 18,
                                  color: Colors.grey.shade300,
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Container(
                              width: double.infinity,
                              height: 14,
                              color: Colors.grey.shade300,
                              margin: const EdgeInsets.only(bottom: 6),
                            ),
                            Container(
                              width: double.infinity,
                              height: 14,
                              color: Colors.grey.shade300,
                              margin: const EdgeInsets.only(bottom: 6),
                            ),
                            Container(
                              width: MediaQuery.of(context).size.width * 0.7,
                              height: 14,
                              color: Colors.grey.shade300,
                            ),
                            const SizedBox(height: 16),
                            Container(
                              width: 80,
                              height: 16,
                              color: Colors.grey.shade200,
                              alignment: Alignment.center,
                            ),
                          ],
                        ),
                      ),
                      // Reviews skeleton section
                      Container(
                        color: const Color(0xFFFFF8E1),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Header skeleton
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                              child: Row(
                                children: [
                                  Container(
                                    width: 32,
                                    height: 20,
                                    color: Colors.grey.shade300,
                                  ),
                                  const SizedBox(width: 8),
                                  Container(
                                    width: 120,
                                    height: 20,
                                    color: Colors.grey.shade300,
                                  ),
                                  const Spacer(),
                                  Container(
                                    width: 60,
                                    height: 20,
                                    color: Colors.grey.shade300,
                                  ),
                                ],
                              ),
                            ),
                            // Skeleton review cards
                            Container(
                              color: Colors.white,
                              padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
                              child: Column(
                                children: List.generate(
                                  3,
                                  (index) => const ShopDetailReviewCardSkeleton(),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            // Optionally: add a skeleton for the bottom action bar
          );
        }
        if (vm.error != null) {
          return Center(child: Text('Error: ${vm.error}'));
        }
        if (vm.product == null) {
          return const Center(child: Text('Product not found'));
        }
        final product = vm.product!;
        return Scaffold(
          backgroundColor: Colors.white,
          body: Stack(
            children: [
              SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Responsive product image
                    SizedBox(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.width * 0.75, // 4:3 ratio, adapts to screen
                      child: PageView.builder(
                        itemCount: product.imageUrls.length,
                        onPageChanged: vm.onImageChanged,
                        itemBuilder: (context, index) {
                          return Image.network(
                            product.imageUrls[index],
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey[200],
                                child: const Icon(
                                  Icons.image_not_supported,
                                  color: Colors.grey,
                                  size: 60,
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ),
                    _buildProductInfo(vm, context),
                    const Divider(height: 1, thickness: 8, color: Color(0xFFF5F5F5)),
                    _buildProductDescription(product),
                    const Divider(height: 1, thickness: 8, color: Color(0xFFF5F5F5)),
                    _buildReviews(product),
                  ],
                ),
              ),
              // Overlay AppBar
              Container(
                height: kToolbarHeight + MediaQuery.of(context).padding.top,
                color: Colors.white.withOpacity(_appBarOpacity),
                child: AppBar(
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  leading: Padding(
                    padding: const EdgeInsets.only(left: 12), // Adjust left spacing here
                    child: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.black),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ),
                  actions: [
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: IconButton(
                        icon: Stack(
                          children: [
                            const Icon(Icons.shopping_cart_outlined, color: Colors.black),
                            Positioned(
                              right: 0,
                              top: 0,
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                constraints: const BoxConstraints(
                                  minWidth: 12,
                                  minHeight: 12,
                                ),
                                child: const Text(
                                  '1',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 8,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ],
                        ),
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(builder: (_) => const ShopCartView()),
                          );
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(right: 12), // Adjust right spacing here
                      child: IconButton(
                        icon: const Icon(Icons.share, color: Colors.black),
                        onPressed: () {},
                      ),
                    ),
                  ],
                  titleSpacing: 0, // You can adjust this value as needed
                ),
              ),
            ],
          ),
          bottomNavigationBar: _buildBottomActionBar(context, vm),
        );
      },
    );
  }

  Widget _buildProductInfo(ShopDetailViewModel vm, BuildContext context) {
    final product = vm.product!;
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 20, 16, 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Price, original price, discount, sold, favorite
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                flex: 0, // Give more space to the price
                child: Text(
                  'RM ${product.currentPrice.toStringAsFixed(2)}',
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width * 0.07, // Make it even bigger
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),
              if (product.originalPrice != null)
                Flexible(
                  child: Text(
                    'RM ${product.originalPrice!.toStringAsFixed(2)}',
                    style: TextStyle(
                      fontSize: MediaQuery.of(context).size.width * 0.045,
                      color: Colors.grey,
                      decoration: TextDecoration.lineThrough,
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              const SizedBox(width: 8),
              if (product.discountPercentage != null)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    '-${product.discountPercentage!.toStringAsFixed(0)}%',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: MediaQuery.of(context).size.width * 0.035,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          // Discount badges
          Row(
            children: [
              Flexible(
                flex: 1,
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      "New User's 20% Discount",
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Flexible(
                flex: 1,
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Text(
                      "Use Cards to get MAX RM10 Off",
                      style: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Title
          Text(
            product.title,
            style: TextStyle(
              fontSize: MediaQuery.of(context).size.width * 0.05, // Responsive font size
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          // Variation
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Variation:',
                style: TextStyle(
                  fontSize: MediaQuery.of(context).size.width * 0.037,
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  product.variation,
                  style: TextStyle(
                    fontSize: MediaQuery.of(context).size.width * 0.037,
                    color: Colors.black87,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProductDescription(product) {
    final int maxLines = _descExpanded ? 100 : 3;
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.description, color: Colors.orange[600]),
              const SizedBox(width: 8),
              const Text(
                'Product Description',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            product.description,
            style: const TextStyle(fontSize: 14),
            maxLines: maxLines,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () => setState(() => _descExpanded = !_descExpanded),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  height: 1,
                  width: 40,
                  color: Colors.grey[300],
                ),
                const SizedBox(width: 8),
                Text(
                  _descExpanded ? 'See Less' : 'See More',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  _descExpanded
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                  color: Colors.grey,
                  size: 18,
                ),
                const SizedBox(width: 8),
                Container(
                  height: 1,
                  width: 40,
                  color: Colors.grey[300],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviews(product) {
    return Container(
      color: const Color(0xFFFFF8E1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Text(
                  product.rating.toStringAsFixed(1),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(width: 4),
                const Icon(Icons.star, color: Colors.amber, size: 20),
                const SizedBox(width: 8),
                const Text(
                  'Product Ratings & Reviews',
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'View All',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.black,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Reviews
          Container(
            color: Colors.white,
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 24), // Add bottom padding here
            child: Column(
              children: product.reviews
                  .map<Widget>((review) => ShopDetailReviewCard(review: review))
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar(BuildContext context, ShopDetailViewModel vm) {
    return SizedBox(
      height: 60, // Taller for a more prominent bottom action bar
      child: Row(
        children: [
          // Chat Now
          Expanded(
            child: SizedBox.expand(
              child: OutlinedButton(
                onPressed: () => vm.onChatNow(context),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.black,
                  side: const BorderSide(color: Colors.grey),
                  padding: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  textStyle: const TextStyle(fontSize: 14),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Icon(Icons.chat_outlined, size: 24),
                    SizedBox(height: 4),
                    Text('Chat Now'),
                  ],
                ),
              ),
            ),
          ),
          // Add to Cart
          Expanded(
            child: SizedBox.expand(
              child: OutlinedButton(
                onPressed: () => vm.onAddToCart(context),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.black,
                  side: const BorderSide(color: Colors.grey),
                  padding: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  textStyle: const TextStyle(fontSize: 14),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Icon(Icons.shopping_cart_outlined, size: 24),
                    SizedBox(height: 4),
                    Text('Add to Cart'),
                  ],
                ),
              ),
            ),
          ),
          // Buy Now
          Expanded(
            child: SizedBox.expand(
              child: ElevatedButton(
                onPressed: () => vm.onBuyNow(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.amber[600],
                  foregroundColor: Colors.black,
                  padding: EdgeInsets.zero,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  textStyle: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                child: const Text('Buy Now'),
              ),
            ),
          ),
        ],
      ),
    );
  }
}