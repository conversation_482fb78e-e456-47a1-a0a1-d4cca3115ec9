import 'package:flutter/material.dart';
import '../../model/cardPack/CardItem.dart';
import 'package:flutter_svg/flutter_svg.dart';

class PhysicalCategoryNav extends StatelessWidget {
  final String selectedCategory;
  final ValueChanged<String> onCategorySelected;
  final Map<String, int> selectedCountMap;
  final List<CardItem> userCards;

  const PhysicalCategoryNav({
    super.key,
    required this.selectedCategory,
    required this.onCategorySelected,
    required this.selectedCountMap,
    required this.userCards,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    // Use screen width to scale icon/text slightly
    double scale = (screenWidth / 390.0).clamp(0.9, 1.1);

    final categories = [
      {
        'key': 'royal',
        'icon': 'assets/icons/cardCategory/King01.svg',
        'activeIcon': 'assets/icons/cardCategory/King02.svg',
      },
      {
        'key': 'knight',
        'icon': 'assets/icons/cardCategory/Knight01.svg',
        'activeIcon': 'assets/icons/cardCategory/Knight02.svg',
      },
      {
        'key': 'wizard',
        'icon': 'assets/icons/cardCategory/Wizard01.svg',
        'activeIcon': 'assets/icons/cardCategory/Wizard02.svg',
      },
      {
        'key': 'smith',
        'icon': 'assets/icons/cardCategory/Smith01.svg',
        'activeIcon': 'assets/icons/cardCategory/Smith02.svg',
      },
      {
        'key': 'elf',
        'icon': 'assets/icons/cardCategory/Elf01.svg',
        'activeIcon': 'assets/icons/cardCategory/Elf02.svg',
      },
    ];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8 * scale, vertical: 10 * scale),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(2, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: categories.map((cat) {
          final isSelected = selectedCategory == cat['key'];
          final String catKey = cat['key']!;
          final selectedCount = userCards
              .where((c) => c.category == catKey && selectedCountMap.containsKey(c.id))
              .length;

          return GestureDetector(
            onTap: () => onCategorySelected(catKey),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Container(
                      padding: EdgeInsets.all(10 * scale),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected ? Colors.amber : Colors.grey[300],
                        boxShadow: isSelected
                            ? [
                                BoxShadow(
                                  color: const Color.fromARGB(73, 255, 193, 7),
                                  blurRadius: 8,
                                  offset: const Offset(0, 3),
                                )
                              ]
                            : [],
                      ),
                      child: SvgPicture.asset(
                        isSelected ? cat['activeIcon']! : cat['icon']!,
                        width: isSelected ? 30 * scale : 26 * scale,
                        height: isSelected ? 30 * scale : 26 * scale,
                        colorFilter: ColorFilter.mode(
                          isSelected ? Colors.white : Colors.grey,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    if (selectedCount > 0)
                      Positioned(
                        top: -4,
                        right: -4,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 5 * scale,
                            vertical: 2 * scale,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.redAccent,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '$selectedCount',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10 * scale,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
                SizedBox(height: 4 * scale),
                Text(
                  catKey[0].toUpperCase() + catKey.substring(1),
                  style: TextStyle(
                    fontSize: 12 * scale,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected ? Colors.amber : Colors.grey,
                  ),
                )
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}
