import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:luckymall/modules/luckydraw/widgets/GroupPurchaseCategory.dart';
import 'package:luckymall/modules/shop/widget/ShopCard.dart';
import 'package:luckymall/data/model/ShoplistModel.dart';
import 'package:luckymall/modules/shop/view/ShopDetailView.dart';
import 'package:luckymall/modules/blindbox/widgets/BlindboxShopCard.dart';
import 'package:luckymall/modules/blindbox/view/BlindboxBuyView.dart';

class GroupPurchaseView extends StatefulWidget {
  const GroupPurchaseView({super.key});

  @override
  State<GroupPurchaseView> createState() => _GroupPurchaseViewState();
}

class _GroupPurchaseViewState extends State<GroupPurchaseView> {
  // Sample group purchase products (Shop)
  final List<ShopProduct> _groupPurchaseProducts = [
    const ShopProduct(
      id: 'gp1',
      title: 'Wireless Bluetooth Headphones - Group Deal',
      imageUrl:
          'https://www.toysplayer.com.my/data/prod/1718774491_banc661852_1.jpg',
      currentPrice: 89.99,
      originalPrice: 149.99,
      discountPercentage: 40,
      rating: 4.8,
      soldCount: 1250,
      maxSavings: 60.00,
      isFavorite: false,
      category: 'Electronics',
      groupBuyEnable: true,
    ),
    const ShopProduct(
      id: 'gp2',
      title: 'Smart Watch Fitness Tracker - Group Purchase',
      imageUrl:
          'https://www.toysplayer.com.my/data/prod/gallery/1730439930_1.jpg',
      currentPrice: 199.99,
      originalPrice: 299.99,
      discountPercentage: 33,
      rating: 4.5,
      soldCount: 890,
      maxSavings: 100.00,
      isFavorite: true,
      category: 'Electronics',
      groupBuyEnable: true,
    ),
    const ShopProduct(
      id: 'gp3',
      title: 'Premium Coffee Maker - Group Buy Special',
      imageUrl:
          'https://www.plushieshop.co.uk/wp-content/uploads/2021/05/1111344994175-e1624151494656.jpg',
      currentPrice: 159.99,
      originalPrice: 249.99,
      discountPercentage: 36,
      rating: 4.7,
      soldCount: 456,
      maxSavings: 90.00,
      isFavorite: false,
      category: 'Kitchen',
      groupBuyEnable: true,
    ),
    const ShopProduct(
      id: 'gp4',
      title: 'Organic Skincare Set - Group Purchase',
      imageUrl:
          'https://ae01.alicdn.com/kf/Sadd4caf41f20437b8b6218ffbf695e0cs.jpg',
      currentPrice: 79.99,
      originalPrice: 129.99,
      discountPercentage: 38,
      rating: 4.6,
      soldCount: 723,
      maxSavings: 50.00,
      isFavorite: false,
      category: 'Beauty',
      groupBuyEnable: true,
    ),
    const ShopProduct(
      id: 'gp5',
      title: 'Wireless Bluetooth Headphones - Group Deal',
      imageUrl:
          'https://down-my.img.susercontent.com/file/my-11134207-7rasj-m12ikbxur4r1fb',
      currentPrice: 89.99,
      originalPrice: 149.99,
      discountPercentage: 40,
      rating: 4.8,
      soldCount: 1250,
      maxSavings: 60.00,
      isFavorite: false,
      category: 'Electronics',
      groupBuyEnable: true,
    ),
    const ShopProduct(
      id: 'gp6',
      title: 'Wireless Bluetooth Headphones - Group Deal',
      imageUrl:
          'https://i5.walmartimages.com/seo/PlayStation-5-PS5-Console-Disc-Version_594285d5-4251-485f-b7d5-25e042253e75.89174ac515a7f3cd24f493f899b404eb.jpeg',
      currentPrice: 89.99,
      originalPrice: 149.99,
      discountPercentage: 40,
      rating: 4.8,
      soldCount: 1250,
      maxSavings: 60.00,
      isFavorite: false,
      category: 'Electronics',
      groupBuyEnable: true,
    ),
    const ShopProduct(
      id: 'gp7',
      title: 'Wireless Bluetooth Headphones - Group Deal',
      imageUrl:
          'https://images-cdn.ubuy.com.my/636f7f85212a3b1beb4b48b0-takara-tomy-ultimate-valkyrie-valtryek.jpg',
      currentPrice: 89.99,
      originalPrice: 149.99,
      discountPercentage: 40,
      rating: 4.8,
      soldCount: 1250,
      maxSavings: 60.00,
      isFavorite: false,
      category: 'Electronics',
      groupBuyEnable: true,
    ),
    const ShopProduct(
      id: 'gp8',
      title: 'Wireless Bluetooth Headphones - Group Deal',
      imageUrl:
          'https://static.wikia.nocookie.net/b-dapedia/images/d/d7/Smash%3DDragold_Official.jpg/revision/latest?cb=20131001164409',
      currentPrice: 89.99,
      originalPrice: 149.99,
      discountPercentage: 40,
      rating: 4.8,
      soldCount: 1250,
      maxSavings: 60.00,
      isFavorite: false,
      category: 'Electronics',
      groupBuyEnable: true,
    ),
  ];

  // Sample group purchase products (Blind Box)
  final List<Map<String, dynamic>> _blindBoxGroupPurchaseProducts = [
    {
      'title': 'Mystery Anime Blind Box',
      'price': 'RM59.99',
      'rating': 4.9,
      'soldCount': 320,
      'maxSavings': 'RM30.00',
      'imageUrl':
          'https://cdn.shopify.com/s/files/1/0257/6087/9356/products/anime-blindbox.jpg',
      'isFavorite': false,
      'isGroupBuy': true,
    },
    {
      'title': 'Cute Plush Blind Box',
      'price': 'RM39.99',
      'rating': 4.7,
      'soldCount': 210,
      'maxSavings': 'RM15.00',
      'imageUrl':
          'https://www.plushieshop.co.uk/wp-content/uploads/2021/05/1111344994175-e1624151494656.jpg',
      'isFavorite': true,
      'isGroupBuy': true,
    },
    {
      'title': 'Gadget Surprise Blind Box',
      'price': 'RM99.99',
      'rating': 4.8,
      'soldCount': 150,
      'maxSavings': 'RM50.00',
      'imageUrl':
          'https://images-cdn.ubuy.com.my/636f7f85212a3b1beb4b48b0-takara-tomy-ultimate-valkyrie-valtryek.jpg',
      'isFavorite': false,
      'isGroupBuy': true,
    },
    {
      'title': 'Stationery Blind Box',
      'price': 'RM29.99',
      'rating': 4.6,
      'soldCount': 400,
      'maxSavings': 'RM10.00',
      'imageUrl':
          'https://static.wikia.nocookie.net/b-dapedia/images/d/d7/Smash%3DDragold_Official.jpg/revision/latest?cb=20131001164409',
      'isFavorite': false,
      'isGroupBuy': true,
    },
    {
      'title': 'Beauty & Skincare Blind Box',
      'price': 'RM79.99',
      'rating': 4.5,
      'soldCount': 180,
      'maxSavings': 'RM40.00',
      'imageUrl':
          'https://ae01.alicdn.com/kf/Sadd4caf41f20437b8b6218ffbf695e0cs.jpg',
      'isFavorite': true,
      'isGroupBuy': true,
    },
  ];

  String _selectedCategory = 'Main Products';

  void onProductTap(BuildContext context, ShopProduct product) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ShopDetailView(productId: product.id),
      ),
    );
  }

  void onFavoriteTap(BuildContext context, ShopProduct product) {
    final index = _groupPurchaseProducts.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      setState(() {
        _groupPurchaseProducts[index] = product.copyWith(
          isFavorite: !product.isFavorite,
        );
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            product.isFavorite
                ? 'Removed from favorites'
                : 'Added to favorites',
          ),
          duration: const Duration(seconds: 1),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(70.0),
        child: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xFFFCD255), Color(0xFFFDE18E), Color(0xFFFFFFFF)],
            ),
          ),
          child: AppBar(
            backgroundColor: Colors.transparent,
            centerTitle: true,
            elevation: 0,
            leading: IconButton(
              icon: SvgPicture.asset(
                'assets/icons/back.svg',
                colorFilter: const ColorFilter.mode(
                  Colors.black,
                  BlendMode.srcIn,
                ),
              ),
              onPressed: () => Navigator.pop(context),
            ),
            title: const Text(
              'Group Purchase',
              style: TextStyle(
                fontSize: 18,
                color: Colors.black,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          // Category Filter
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            child: GroupPurchaseCategory(
              onCategorySelected: (category) {
                setState(() {
                  _selectedCategory = category;
                });
              },
              initialCategory: _selectedCategory,
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: GridView.builder(
                padding: const EdgeInsets.only(top: 10, bottom: 20),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.65,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                itemCount: _selectedCategory == 'Main Products'
                    ? _groupPurchaseProducts.length
                    : _blindBoxGroupPurchaseProducts.length,
                itemBuilder: (context, index) {
                  if (_selectedCategory == 'Main Products') {
                    final product = _groupPurchaseProducts[index];
                    return ShopCard(
                      product: product,
                      onTap: () => onProductTap(context, product),
                      onFavorite: () => onFavoriteTap(context, product),
                    );
                  } else {
                    final product = _blindBoxGroupPurchaseProducts[index];
                    return BlindboxShopCard(
                      title: product['title'] as String,
                      price: product['price'] as String,
                      rating: product['rating'] as double,
                      soldCount: product['soldCount'] as int,
                      maxSavings: product['maxSavings'] as String?,
                      imageUrl: product['imageUrl'] as String?,
                      isGroupBuy: true,
                      isFavorite: product['isFavorite'] as bool,
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => BlindboxBuyView(
                              isGroupBuy: true,
                              product: product,
                            ),
                          ),
                        );
                      },
                      onFavorite: () {
                        setState(() {
                          product['isFavorite'] =
                              !(product['isFavorite'] as bool);
                        });
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              product['isFavorite']
                                  ? 'Added to favorites'
                                  : 'Removed from favorites',
                            ),
                            duration: const Duration(seconds: 1),
                          ),
                        );
                      },
                    );
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
