import 'package:flutter/material.dart';
import 'AddAddressView.dart';
import 'UpdateAddress.dart';
import 'widgets/CustomAppBar.dart';
import 'widgets/AddressCard.dart';

class MyAddressView extends StatelessWidget {
  const MyAddressView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final List<Map<String, dynamic>> addresses = [
      {
        'name': '<PERSON>',
        'phone': '0138604691',
        'address':
            'Lot 1, Blok A Tkt 1 Batu 4, Taman Indah Jaya, Jln Lintas Selatan 90000 Sandakan, Sabah',
        'postal': '90000',
        'isDefault': true,
      },
      {
        'name': '<PERSON>',
        'phone': '0138604691',
        'address':
            'No. 27, Jalan Kenari 3, Taman Indah Permai, 90000 Sandakan, Sabah',
        'postal': '90000',
        'isDefault': false,
      },
    ];

    return Scaffold(
      appBar: CustomAppBar(
        title: 'Manage Addresses',
        onAdd: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const AddAddressView()),
          );
        },
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(12),
        itemCount: addresses.length,
        itemBuilder: (context, index) {
          final address = addresses[index];
          return AddressCard(
            address: address,
            onTap: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => UpdateAddressView(address: address),
                ),
              );
            },
          );
        },
      ),
      backgroundColor: Colors.white,
    );
  }
}
