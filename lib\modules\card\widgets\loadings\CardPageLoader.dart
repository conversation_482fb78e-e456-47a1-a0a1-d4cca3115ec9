import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../view/CardPage.dart';

class CardPageLoader extends StatefulWidget {
  const CardPageLoader({super.key});

  @override
  State<CardPageLoader> createState() => _CardPageLoaderState();
}

class _CardPageLoaderState extends State<CardPageLoader> {
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!isLoading) {
      return const CardPage();
    }

    final screenWidth = MediaQuery.of(context).size.width;
    final double aspectRatio = 390 / 482;
    final double sideMargin = screenWidth * 0.06; // ~24 on 390px width
    final double imageHeight = screenWidth / aspectRatio;
    final double buyNowWidth = screenWidth * 0.85; // 85% of screen width
    final double buyNowHeight = 55 * (screenWidth / 390); // scale with width
    final double circleRadius = 20 * (screenWidth / 390);
    final double cardButtonHeight = 60 * (screenWidth / 390);
    final double cardButtonSpacing = 20 * (screenWidth / 390);
    final double exchangeButtonWidth = 120 * (screenWidth / 390);
    final double exchangeButtonHeight = 40 * (screenWidth / 390);

    return Scaffold(
      backgroundColor: Colors.white,
      body: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            child: Column(
              children: [
                // Stack for image, circle buttons, and BUY NOW button
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    // Image shimmer area
                    Padding(
                      padding: EdgeInsets.only(top: 50 * (screenWidth / 390)),
                      child: AspectRatio(
                        aspectRatio: aspectRatio,
                        child: Shimmer.fromColors(
                          baseColor: Colors.grey.shade300,
                          highlightColor: Colors.grey.shade100,
                          child: Container(
                            width: double.infinity,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    // Circle buttons (info/support)
                    Positioned(
                      top: 10 * (screenWidth / 390) + MediaQuery.of(context).padding.top,
                      right: 16 * (screenWidth / 390),
                      child: Row(
                        children: [
                          Shimmer.fromColors(
                            baseColor: Colors.grey.shade300,
                            highlightColor: Colors.grey.shade100,
                            child: CircleAvatar(
                              radius: circleRadius,
                              backgroundColor: Colors.white,
                            ),
                          ),
                          SizedBox(width: 12 * (screenWidth / 390)),
                          Shimmer.fromColors(
                            baseColor: Colors.grey.shade300,
                            highlightColor: Colors.grey.shade100,
                            child: CircleAvatar(
                              radius: circleRadius,
                              backgroundColor: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // "BUY NOW" shimmer button (overlaps image bottom)
                    Positioned(
                      top: imageHeight + 20 * (screenWidth / 390),
                      left: (screenWidth - buyNowWidth) / 2,
                      child: Shimmer.fromColors(
                        baseColor: Colors.grey.shade300,
                        highlightColor: Colors.grey.shade100,
                        child: Container(
                          width: buyNowWidth,
                          height: buyNowHeight,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10 * (screenWidth / 390)),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 50 * (screenWidth / 390)), // space under BuyNow

                // Transfer + Card Pack shimmer buttons
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: sideMargin),
                  child: Row(
                    children: List.generate(2, (index) {
                      return Expanded(
                        child: Padding(
                          padding: EdgeInsets.only(right: index == 0 ? cardButtonSpacing : 0),
                          child: Shimmer.fromColors(
                            baseColor: Colors.grey.shade300,
                            highlightColor: Colors.grey.shade100,
                            child: Container(
                              height: cardButtonHeight,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12 * (screenWidth / 390)),
                              ),
                            ),
                          ),
                        ),
                      );
                    }),
                  ),
                ),
                SizedBox(height: 40 * (screenWidth / 390)),
                // CardExchangeButton shimmer
                Align(
                  alignment: Alignment.centerRight,
                  child: Padding(
                    padding: EdgeInsets.only(right: sideMargin),
                    child: Shimmer.fromColors(
                      baseColor: Colors.grey.shade300,
                      highlightColor: Colors.grey.shade100,
                      child: Container(
                        width: exchangeButtonWidth,
                        height: exchangeButtonHeight,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12 * (screenWidth / 390)),
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 30 * (screenWidth / 390)),
              ],
            ),
          );
        },
      ),
    );
  }
}