import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'AddAddressView.dart';
import 'widgets/CustomAppBar.dart';
import 'widgets/EmptyAddress.dart';

class MyAddressView extends StatelessWidget {
  const MyAddressView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'Manage Addresses',
        onAdd: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const AddAddressView()),
          );
        },
      ),
      body: const EmptyAddressBody(),
      backgroundColor: Colors.white,
    );
  }
}
