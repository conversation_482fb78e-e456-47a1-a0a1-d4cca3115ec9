import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class OpenSoonCard extends StatelessWidget {
  final List<OpenSoonProduct> products;
  final VoidCallback? onProductTap;

  const OpenSoonCard({Key? key, required this.products, this.onProductTap})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      child: Stack(
        children: [
          // Main yellow container
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: const Color(0xFFFFBF00), // Bright yellow background
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  const SizedBox(height: 20), // Space for overlapping banner
                  // White inner container
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          offset: const Offset(0, 2),
                          blurRadius: 8,
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: [
                          // Horizontally scrollable product cards
                          SizedBox(
                            height: 200, // Fixed height for the scrollable area
                            child: ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: products.length,
                              itemBuilder: (context, index) {
                                return Container(
                                  width: 160, // Fixed width for each card
                                  margin: const EdgeInsets.only(right: 12),
                                  child: _buildProductCard(products[index]),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // "Opening Soon" banner (overlapping)
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFFFFD700), // Dark yellow
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      offset: const Offset(0, 2),
                      blurRadius: 4,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: const Text(
                  'Opening Soon',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductCard(OpenSoonProduct product) {
    return GestureDetector(
      onTap: onProductTap,
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFFFFF8DC), // Light yellow/cream background
          borderRadius: BorderRadius.circular(12),
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8), // Space for overlapping label
                // Product image area
                Container(
                  height: 80,
                  width: double.infinity,
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Stack(
                      children: [
                        // Product image
                        CachedNetworkImage(
                          imageUrl: product.imageUrl,
                          fit: BoxFit.cover,
                          width: double.infinity,
                          height: double.infinity,
                          placeholder: (context, url) => Container(
                            color: Colors.grey[100],
                            child: const Center(
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Color(0xFFFFBF00),
                              ),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            color: Colors.grey[200],
                            child: Icon(
                              Icons.image_not_supported,
                              color: Colors.grey[400],
                              size: 30,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Product details with flex to push points to bottom
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Product name
                        Text(
                          product.productName,
                          style: const TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),

                        const Spacer(), // This pushes the points to the bottom
                        // Points
                        Text(
                          '${product.pointValue} Pts',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),

                        const SizedBox(height: 6),

                        // Progress bar (nearly full)
                        Container(
                          height: 4,
                          decoration: BoxDecoration(
                            color: const Color(
                              0xFFFFF8DC,
                            ), // Light yellow/cream
                            borderRadius: BorderRadius.circular(2),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: 0.95, // Nearly full (95% progress)
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(2),
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFFFFA500), // Bright orange
                                    Color(0xFFFF8C00), // Darker orange
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            // "COMING SOON" label (overlapping)
            Positioned(
              top: 0,
              left: 8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  'COMING SOON',
                  style: TextStyle(
                    fontSize: 8,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class OpenSoonProduct {
  final String productName;
  final String imageUrl;
  final int pointValue;

  const OpenSoonProduct({
    required this.productName,
    required this.imageUrl,
    required this.pointValue,
  });
}

// Example usage widget
class OpenSoonSection extends StatelessWidget {
  const OpenSoonSection({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Sample data matching the image
    final List<OpenSoonProduct> products = [
      const OpenSoonProduct(
        productName: 'Mouse Razer Pulsar X16 Gaming Mouse',
        imageUrl:
            'https://example.com/razer-mouse.jpg', // Replace with actual image URL
        pointValue: 20000,
      ),
      const OpenSoonProduct(
        productName: 'Vacuum S8000',
        imageUrl:
            'https://example.com/vacuum.jpg', // Replace with actual image URL
        pointValue: 20000,
      ),
      const OpenSoonProduct(
        productName: 'Mouse Razer X16 Gaming Mouse',
        imageUrl:
            'https://example.com/razer-mouse-2.jpg', // Replace with actual image URL
        pointValue: 20000,
      ),
    ];

    return OpenSoonCard(
      products: products,
      onProductTap: () {
        // Handle product tap
        print('Product tapped');
      },
    );
  }
}

// Demo page to showcase the widget
class OpenSoonDemoPage extends StatelessWidget {
  const OpenSoonDemoPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Opening Soon Demo'),
        backgroundColor: const Color(0xFFFFBF00),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 20),
            const OpenSoonSection(),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'This widget shows products that are opening soon with progress bars nearly at 95% completion, indicating they are about to be revealed.',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
