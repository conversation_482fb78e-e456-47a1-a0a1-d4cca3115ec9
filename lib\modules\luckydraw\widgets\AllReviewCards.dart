import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class AllReviewCards extends StatefulWidget {
  final String selectedCategory;

  const AllReviewCards({Key? key, this.selectedCategory = 'All'})
    : super(key: key);

  @override
  State<AllReviewCards> createState() => _AllReviewCardsState();
}

class _AllReviewCardsState extends State<AllReviewCards> {
  // Sample review data - mix of reviews with and without images
  final List<Map<String, dynamic>> allReviews = [
    {
      'reviewerName': '<PERSON>',
      'avatarUrl': 'https://randomuser.me/api/portraits/men/32.jpg',
      'date': '2025 / 02 / 15',
      'rating': 5,
      'variation': 'White Large',
      'reviewText':
          'Received with perfect condition. Modern and square design make it more space for every dishes. RECOMMEND 👍',
      'imageUrls': [
        'https://img.freepik.com/free-photo/black-friday-elements-assortment_23-2149074075.jpg?semt=ais_hybrid&w=740',
        'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop',
        'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
      ],
      'helpfulCount': 22,
      'category': 'Main Product',
    },
    {
      'reviewerName': 'Li Mei',
      'avatarUrl': 'https://randomuser.me/api/portraits/women/44.jpg',
      'date': '2025 / 02 / 14',
      'rating': 4,
      'variation': 'Black Medium',
      'reviewText':
          'Great quality product! Fast shipping and excellent customer service. Will definitely buy again.',
      'imageUrls': [], // No images
      'helpfulCount': 15,
      'category': 'Point Product',
    },
    {
      'reviewerName': 'John Smith',
      'avatarUrl': 'https://randomuser.me/api/portraits/men/85.jpg',
      'date': '2025 / 02 / 13',
      'rating': 5,
      'variation': 'Blue Small',
      'reviewText':
          'Amazing product! Exceeded my expectations. The build quality is fantastic and it works perfectly.',
      'imageUrls': [
        'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
      ],
      'helpfulCount': 8,
      'category': 'Main Product',
    },
    {
      'reviewerName': 'Sarah Johnson',
      'avatarUrl': 'https://randomuser.me/api/portraits/women/68.jpg',
      'date': '2025 / 02 / 12',
      'rating': 4,
      'variation': 'Red Large',
      'reviewText':
          'Very satisfied with this purchase. Good value for money and arrived quickly. The product quality is excellent.',
      'imageUrls': [], // No images
      'helpfulCount': 12,
      'category': 'Blind Box',
    },
    {
      'reviewerName': 'Wang Lei',
      'avatarUrl': 'https://randomuser.me/api/portraits/men/12.jpg',
      'date': '2025 / 02 / 11',
      'rating': 5,
      'variation': 'Green Medium',
      'reviewText':
          'Perfect! Exactly as described. High quality materials and excellent craftsmanship.',
      'imageUrls': [
        'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=400&fit=crop',
        'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop',
      ],
      'helpfulCount': 5,
      'category': 'Lucky Group',
    },
    {
      'reviewerName': 'Chen Xiao',
      'avatarUrl': 'https://randomuser.me/api/portraits/women/23.jpg',
      'date': '2025 / 02 / 10',
      'rating': 3,
      'variation': 'Purple Small',
      'reviewText':
          'Decent product but took longer than expected to arrive. Quality is acceptable for the price.',
      'imageUrls': [], // No images
      'helpfulCount': 3,
      'category': 'Point Product',
    },
    {
      'reviewerName': 'Mike Johnson',
      'avatarUrl': 'https://randomuser.me/api/portraits/men/67.jpg',
      'date': '2025 / 02 / 09',
      'rating': 5,
      'variation': 'Orange Large',
      'reviewText':
          'Outstanding quality and service! Will definitely recommend to friends and family.',
      'imageUrls': [
        'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=400&h=400&fit=crop',
      ],
      'helpfulCount': 18,
      'category': 'Main Product',
    },
    {
      'reviewerName': 'Anna Smith',
      'avatarUrl': 'https://randomuser.me/api/portraits/women/89.jpg',
      'date': '2025 / 02 / 08',
      'rating': 4,
      'variation': 'Yellow Medium',
      'reviewText':
          'Good product overall. Nice design and functionality. Shipping was fast and packaging was secure.',
      'imageUrls': [], // No images
      'helpfulCount': 7,
      'category': 'Blind Box',
    },
    {
      'reviewerName': 'David Brown',
      'avatarUrl': 'https://randomuser.me/api/portraits/men/45.jpg',
      'date': '2025 / 02 / 07',
      'rating': 5,
      'variation': 'Pink Large',
      'reviewText':
          'Excellent product! Exactly what I was looking for. Great value for money.',
      'imageUrls': [
        'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop',
        'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
        'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=400&fit=crop',
      ],
      'helpfulCount': 25,
      'category': 'Lucky Group',
    },
    {
      'reviewerName': 'Liu Yan',
      'avatarUrl': 'https://randomuser.me/api/portraits/women/56.jpg',
      'date': '2025 / 02 / 06',
      'rating': 4,
      'variation': 'Gray Small',
      'reviewText':
          'Nice product with good build quality. Arrived on time and well packaged.',
      'imageUrls': [], // No images
      'helpfulCount': 11,
      'category': 'Point Product',
    },
  ];

  // Filter reviews based on selected category
  List<Map<String, dynamic>> get filteredReviews {
    if (widget.selectedCategory == 'All') {
      return allReviews;
    }
    return allReviews
        .where((review) => review['category'] == widget.selectedCategory)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375; // base width 375
    double scaleH(double value) =>
        value * screenHeight / 812; // base height 812
    double scaleText(double value) => value * screenWidth / 375;

    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // Reviews List
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(vertical: scaleH(8)),
              itemCount: filteredReviews.length,
              itemBuilder: (context, index) {
                final review = filteredReviews[index];
                return Container(
                  margin: EdgeInsets.only(bottom: scaleH(12)),
                  child: Column(
                    children: [
                      // Custom Review Card
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: scaleW(16)),
                        padding: EdgeInsets.all(scaleW(16)),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(scaleW(12)),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: scaleW(8),
                              offset: Offset(0, scaleH(2)),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // User info and rating
                            Row(
                              children: [
                                CircleAvatar(
                                  radius: scaleW(20),
                                  backgroundImage:
                                      review['avatarUrl'].isNotEmpty
                                      ? NetworkImage(review['avatarUrl'])
                                      : null,
                                  backgroundColor: Colors.grey[300],
                                  child: review['avatarUrl'].isEmpty
                                      ? Icon(
                                          Icons.person,
                                          size: scaleW(20),
                                          color: Colors.grey,
                                        )
                                      : null,
                                ),
                                SizedBox(width: scaleW(12)),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        review['reviewerName'],
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: scaleText(14),
                                        ),
                                      ),
                                      SizedBox(height: scaleH(4)),
                                      Row(
                                        children: [
                                          // Rating stars
                                          Row(
                                            children: List.generate(
                                              5,
                                              (index) => Icon(
                                                Icons.star,
                                                color: index < review['rating']
                                                    ? Colors.amber
                                                    : Colors.grey[300],
                                                size: scaleW(16),
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: scaleW(80)),
                                          Text(
                                            review['date'],
                                            style: TextStyle(
                                              color: Colors.grey[600],
                                              fontSize: scaleText(12),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: scaleH(12)),

                            // Variation
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: scaleW(8),
                                vertical: scaleH(4),
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(scaleW(6)),
                              ),
                              child: Text(
                                'Variation: ${review['variation']}',
                                style: TextStyle(
                                  color: Colors.grey[700],
                                  fontSize: scaleText(12),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            SizedBox(height: scaleH(12)),

                            // Review text
                            Text(
                              review['reviewText'],
                              style: TextStyle(
                                fontSize: scaleText(13),
                                height: 1.3,
                                color: Colors.black87,
                              ),
                            ),

                            // Images (if any)
                            if (review['imageUrls'].isNotEmpty) ...[
                              SizedBox(height: scaleH(12)),
                              SizedBox(
                                height: scaleH(80),
                                child: ListView.builder(
                                  scrollDirection: Axis.horizontal,
                                  itemCount: review['imageUrls'].length,
                                  itemBuilder: (context, imageIndex) {
                                    return Container(
                                      margin: EdgeInsets.only(
                                        right:
                                            imageIndex <
                                                review['imageUrls'].length - 1
                                            ? scaleW(8)
                                            : 0,
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(
                                          scaleW(8),
                                        ),
                                        child: CachedNetworkImage(
                                          imageUrl:
                                              review['imageUrls'][imageIndex],
                                          width: scaleW(80),
                                          height: scaleH(80),
                                          fit: BoxFit.cover,
                                          placeholder: (context, url) =>
                                              Container(
                                                width: scaleW(80),
                                                height: scaleH(80),
                                                color: Colors.grey[200],
                                                child: Center(
                                                  child: SizedBox(
                                                    width: scaleW(20),
                                                    height: scaleW(20),
                                                    child:
                                                        CircularProgressIndicator(
                                                          strokeWidth: 2,
                                                        ),
                                                  ),
                                                ),
                                              ),
                                          errorWidget: (context, url, error) =>
                                              Container(
                                                width: scaleW(80),
                                                height: scaleH(80),
                                                color: Colors.grey[200],
                                                child: Icon(
                                                  Icons.broken_image,
                                                  color: Colors.grey,
                                                  size: scaleW(24),
                                                ),
                                              ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],

                            SizedBox(height: scaleH(12)),

                            // Helpful section
                            Row(
                              children: [
                                const Spacer(),
                                Row(
                                  children: [
                                    Icon(
                                      Icons.thumb_up_outlined,
                                      size: scaleW(16),
                                      color: Colors.grey,
                                    ),
                                    SizedBox(width: scaleW(4)),
                                    Text(
                                      'Helpful (${review['helpfulCount']?.toString() ?? '0'})',
                                      style: TextStyle(
                                        color: Colors.grey,
                                        fontSize: scaleText(12),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // Separator (except for last item)
                      if (index < filteredReviews.length - 1)
                        Container(
                          margin: EdgeInsets.only(top: scaleH(12)),
                          height: 1,
                          color: Colors.grey[100],
                        ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

// Widget to display helpful count
class ReviewSummary extends StatelessWidget {
  final int totalReviews;
  final double averageRating;
  final int helpfulCount;

  const ReviewSummary({
    Key? key,
    required this.totalReviews,
    required this.averageRating,
    required this.helpfulCount,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    double scaleW(double value) => value * screenWidth / 375; // base width 375
    double scaleH(double value) =>
        value * screenHeight / 812; // base height 812
    double scaleText(double value) => value * screenWidth / 375;

    return Container(
      padding: EdgeInsets.all(scaleW(16)),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(scaleW(12)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Column(
            children: [
              Text(
                totalReviews.toString(),
                style: TextStyle(
                  fontSize: scaleText(18),
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              Text(
                'Reviews',
                style: TextStyle(
                  fontSize: scaleText(12),
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          Column(
            children: [
              Row(
                children: [
                  Text(
                    averageRating.toStringAsFixed(1),
                    style: TextStyle(
                      fontSize: scaleText(18),
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  SizedBox(width: scaleW(4)),
                  Icon(Icons.star, color: Colors.amber, size: scaleW(16)),
                ],
              ),
              Text(
                'Average',
                style: TextStyle(
                  fontSize: scaleText(12),
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          Column(
            children: [
              Text(
                helpfulCount.toString(),
                style: TextStyle(
                  fontSize: scaleText(18),
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              Text(
                'Helpful',
                style: TextStyle(
                  fontSize: scaleText(12),
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
