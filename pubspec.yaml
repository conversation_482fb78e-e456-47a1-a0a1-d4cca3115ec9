name: luckymall
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter 

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_riverpod: ^2.6.1
  dio: ^5.8.0+1
  go_router: ^16.0.0
  freezed_annotation: ^3.1.0
  json_annotation: ^4.9.0
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  lottie: ^3.3.1
  image_picker: ^1.1.2
  path_provider: ^2.1.5
  permission_handler: ^12.0.1
  connectivity_plus: ^6.1.4
  flutter_svg: ^2.2.0
  shared_preferences: ^2.5.3
  country_flags: ^3.3.0
  flutter_staggered_grid_view: ^0.7.0
  flutter_podium: ^1.0.5
  provider: ^6.1.1
  carousel_slider: ^5.1.1
  flutter_switch: ^0.3.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  build_runner: ^2.5.4
  freezed: ^3.1.0
  json_serializable: ^6.9.5
  mocktail: ^1.0.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  
  # To add assets to your application, add an assets section, like this:
  assets: 
    - assets/
    - assets/images/
    - assets/images/card/imageSlider/
    - assets/images/card/cardType/fantasy/royals/
    - assets/images/card/cardType/fantasy/knight/
    - assets/images/card/cardType/fantasy/wizard/
    - assets/lottie/
    - assets/icons/
    - assets/icons/cardCategory/
    - assets/icons/checkOutCard/
    - assets/fonts/
    - assets/icons/voucher.svg
    - assets/icons/Expired Stamp.svg
    - assets/icons/Exchange.svg
    - assets/icons/Face Verification.svg
    - assets/icons/Lucky Draw Group.svg
    - assets/icons/My Favorite.svg
    - assets/icons/My Order.svg
    - assets/icons/My Review.svg
    - assets/icons/My Sharing.svg
    - assets/icons/Participant Record.svg
    - assets/icons/Tutorial.svg
    - assets/icons/Voucher.svg
    - assets/icons/Address.svg
    - assets/icons/Check In.svg
    - assets/icons/My Order.svg
    - assets/icons/Invite Friends.svg
    - assets/icons/Balance.svg
    - assets/icons/Customer Support.svg
    - assets/icons/Notification.svg 
    - assets/icons/Empty Profile.svg
    - assets/icons/Settings 02.svg
    - assets/icons/Delete.svg
    - assets/images/InviteFriendsBanner.svg
    - assets/icons/Click.svg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package