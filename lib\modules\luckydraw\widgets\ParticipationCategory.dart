import 'package:flutter/material.dart';

class ParticipationCategory extends StatefulWidget {
  final Function(String)? onCategorySelected;
  final String? initialCategory;

  const ParticipationCategory({
    Key? key,
    this.onCategorySelected,
    this.initialCategory,
  }) : super(key: key);

  @override
  State<ParticipationCategory> createState() => _ParticipationCategoryState();
}

class _ParticipationCategoryState extends State<ParticipationCategory> {
  String selectedCategory = 'My Participation';

  final List<String> categories = ['My Participation', 'My Prize'];

  @override
  void initState() {
    super.initState();
    if (widget.initialCategory != null) {
      selectedCategory = widget.initialCategory!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = selectedCategory == category;
          final isLast = index == categories.length - 1;

          return Container(
            margin: EdgeInsets.only(right: isLast ? 0 : 12),
            child: GestureDetector(
              onTap: () {
                setState(() {
                  selectedCategory = category;
                });
                widget.onCategorySelected?.call(category);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: isSelected ? const Color(0xFFffbf00) : Colors.white,
                  border: Border.all(
                    color: isSelected
                        ? const Color(0xFFffbf00)
                        : Colors.grey.shade300,
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    category,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? Colors.black : Colors.grey.shade700,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
