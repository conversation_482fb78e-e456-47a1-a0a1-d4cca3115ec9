import 'package:flutter/material.dart';

class DrawRecordCard extends StatelessWidget {
  final String username;
  final String productTitle;
  final String date;
  final String time;
  final String period;
  final String totalShares;
  final String batchNumber;
  final String participationPoint;
  final String winningName;
  final String imageUrl;

  const DrawRecordCard({
    super.key,
    required this.username,
    required this.productTitle,
    required this.date,
    required this.time,
    required this.period,
    required this.totalShares,
    required this.batchNumber,
    required this.participationPoint,
    required this.winningName,
    required this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    // Helper for scaling
    double scaleW(double value) => value * screenWidth / 375; // base width 375
    double scaleH(double value) =>
        value * screenHeight / 812; // base height 812
    double scaleText(double value) => value * screenWidth / 375;

    return Container(
      margin: EdgeInsets.symmetric(horizontal: scaleW(16), vertical: scaleH(6)),
      padding: EdgeInsets.all(scaleW(8)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(scaleW(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: scaleW(6),
            offset: Offset(0, scaleH(2)),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top Row: Avatar + User + Title
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: scaleW(18),
                backgroundColor: Colors.grey.shade400,
                child: Text(
                  'U',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: scaleText(14),
                  ),
                ),
              ),
              SizedBox(width: scaleW(12)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'User $username',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: scaleText(13),
                        color: Colors.grey.shade600,
                      ),
                    ),
                    SizedBox(height: scaleH(4)),
                    Text(
                      productTitle,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: scaleText(13),
                        color: Colors.black,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: scaleH(16)),

          // Image and Description Grid
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left: Product Image
              Container(
                height: scaleW(100),
                width: scaleW(100),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(scaleW(8)),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(scaleW(8)),
                  child: Image.network(
                    imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey.shade200,
                        child: Icon(
                          Icons.image_not_supported,
                          color: Colors.grey,
                          size: scaleW(40),
                        ),
                      );
                    },
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        color: Colors.grey.shade200,
                        child: Center(
                          child: SizedBox(
                            width: scaleW(24),
                            height: scaleW(24),
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              SizedBox(width: scaleW(8)),

              // Right: Detail Info in Two Columns
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildInfoRow('Date:', date, scaleText),
                          SizedBox(height: scaleH(6)),
                          _buildInfoRow('Time:', time, scaleText),
                          SizedBox(height: scaleH(6)),
                          _buildInfoRow('Period:', period, scaleText),
                        ],
                      ),
                    ),
                    SizedBox(width: scaleW(8)),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildInfoRow(
                            'Total Shares:',
                            totalShares,
                            scaleText,
                          ),
                          SizedBox(height: scaleH(6)),
                          _buildInfoRow(
                            'Batch Number:',
                            batchNumber,
                            scaleText,
                          ),
                          SizedBox(height: scaleH(6)),
                          _buildInfoRow(
                            'Participation Point:',
                            participationPoint,
                            scaleText,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: scaleH(8)),

          // Winning Name Section
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              vertical: scaleH(6),
              horizontal: scaleW(16),
            ),
            decoration: BoxDecoration(
              color: const Color(0xFF81C784).withOpacity(0.3),
              borderRadius: BorderRadius.circular(scaleW(8)),
            ),
            child: RichText(
              text: TextSpan(
                text: 'Winning Name: ',
                style: TextStyle(
                  color: const Color(0xFF2E7D32),
                  fontWeight: FontWeight.bold,
                  fontSize: scaleText(13),
                ),
                children: [
                  TextSpan(
                    text: winningName,
                    style: TextStyle(
                      color: Colors.black87,
                      fontWeight: FontWeight.bold,
                      fontSize: scaleText(13),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value,
    double Function(double) scaleText,
  ) {
    return RichText(
      text: TextSpan(
        text: label,
        style: TextStyle(
          color: Colors.grey.shade600,
          fontSize: scaleText(11),
          fontWeight: FontWeight.w500,
        ),
        children: [
          TextSpan(
            text: ' $value',
            style: TextStyle(
              color: Colors.black87,
              fontSize: scaleText(11),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
