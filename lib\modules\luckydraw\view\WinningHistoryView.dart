import 'package:flutter/material.dart';
import 'package:luckymall/modules/luckydraw/widgets/customAppBar.dart';
import 'package:luckymall/modules/luckydraw/widgets/DrawRecordCard.dart';

class WinningHistoryView extends StatelessWidget {
  const WinningHistoryView({super.key});

  @override
  Widget build(BuildContext context) {
    // Control whether to show records or empty state
    bool hasRecords = true; // Set to false to show empty state

    // Sample winning records list - replace with actual data
    final List<Map<String, String>> winningRecords = hasRecords
        ? [
            {
              'username': 'User123',
              'productTitle': 'iPhone 15 Pro Max 256GB - Natural Titanium',
              'date': '2024-01-15',
              'time': '14:30',
              'period': '001',
              'totalShares': '1,250',
              'batchNumber': '001',
              'participationPoint': '50',
              'winningName': 'Jo*****n',
              'imageUrl':
                  'https://image.made-in-china.com/365f3j00eQuCodqJMscr/3-Inch-IPS-Touch-Screen-2000mAh-Android-4G-LTE-Mini-Smartphone-4GB-RAM-128GB-ROM.webp',
            },
            {
              'username': 'User_W',
              'productTitle': 'iPhone 15 Pro Max 256GB - Natural Titanium',
              'date': '2024-01-14',
              'time': '16:45',
              'period': '001',
              'totalShares': '1,250',
              'batchNumber': '103',
              'participationPoint': '75',
              'winningName': 'Sa*****n',
              'imageUrl':
                  'https://image.made-in-china.com/365f3j00eQuCodqJMscr/3-Inch-IPS-Touch-Screen-2000mAh-Android-4G-LTE-Mini-Smartphone-4GB-RAM-128GB-ROM.webp',
            },
            {
              'username': 'User_R',
              'productTitle': 'iPhone 15 Pro Max 256GB - Natural Titanium',
              'date': '2024-01-13',
              'time': '10:20',
              'period': '001',
              'totalShares': '1,250',
              'batchNumber': '003',
              'participationPoint': '100',
              'winningName': 'Mi*****n',
              'imageUrl':
                  'https://image.made-in-china.com/365f3j00eQuCodqJMscr/3-Inch-IPS-Touch-Screen-2000mAh-Android-4G-LTE-Mini-Smartphone-4GB-RAM-128GB-ROM.webp',
            },
            {
              'username': 'User_T',
              'productTitle': 'iPhone 15 Pro Max 256GB - Natural Titanium',
              'date': '2024-01-12',
              'time': '13:15',
              'period': '001',
              'totalShares': '1,250',
              'batchNumber': '004',
              'participationPoint': '25',
              'winningName': 'Li*****n',
              'imageUrl':
                  'https://image.made-in-china.com/365f3j00eQuCodqJMscr/3-Inch-IPS-Touch-Screen-2000mAh-Android-4G-LTE-Mini-Smartphone-4GB-RAM-128GB-ROM.webp',
            },
            {
              'username': 'User_G',
              'productTitle': 'iPhone 15 Pro Max 256GB - Natural Titanium',
              'date': '2024-01-11',
              'time': '19:30',
              'period': '001',
              'totalShares': '1,250',
              'batchNumber': '005',
              'participationPoint': '80',
              'winningName': 'Al*****wn',
              'imageUrl':
                  'https://image.made-in-china.com/365f3j00eQuCodqJMscr/3-Inch-IPS-Touch-Screen-2000mAh-Android-4G-LTE-Mini-Smartphone-4GB-RAM-128GB-ROM.webp',
            },
          ]
        : [];

    return Scaffold(
      appBar: CustomAppBar(title: 'Winning History'),
      body: winningRecords.isEmpty
          ? _buildEmptyState()
          : _buildWinningList(winningRecords),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 150,
            height: 150,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/NoRecord.png'),
                fit: BoxFit.contain,
              ),
            ),
          ),
          const SizedBox(height: 24),
          // No Records Text
          Text(
            'No Records',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWinningList(List<Map<String, String>> records) {
    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: records.length,
            itemBuilder: (context, index) {
              final record = records[index];
              return DrawRecordCard(
                username: record['username']!,
                productTitle: record['productTitle']!,
                date: record['date']!,
                time: record['time']!,
                period: record['period']!,
                totalShares: record['totalShares']!,
                batchNumber: record['batchNumber']!,
                participationPoint: record['participationPoint']!,
                winningName: record['winningName']!,
                imageUrl: record['imageUrl']!,
              );
            },
          ),
        ),
      ],
    );
  }
}
