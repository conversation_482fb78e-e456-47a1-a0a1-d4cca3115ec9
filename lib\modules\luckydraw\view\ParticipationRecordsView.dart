import 'package:flutter/material.dart';
import 'package:luckymall/modules/luckydraw/widgets/customAppBar.dart';
import 'package:luckymall/modules/luckydraw/widgets/ParticipationCategory.dart';
import 'package:luckymall/modules/luckydraw/widgets/ParticipationCard.dart';
import 'package:luckymall/modules/luckydraw/view/ClaimVoucherView.dart';

class ParticipationRecordsView extends StatefulWidget {
  const ParticipationRecordsView({super.key});

  @override
  State<ParticipationRecordsView> createState() =>
      _ParticipationRecordsViewState();
}

class _ParticipationRecordsViewState extends State<ParticipationRecordsView> {
  String selectedCategory = 'My Participation';

  // Sample participation data
  final List<Map<String, dynamic>> participationData = [
    {
      'productName':
          'Mouse Razer Pulsar X120 With Ergonomic Functional (PAW3360 Sensor)',
      'imageUrl':
          'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
      'date': '2025-06-30',
      'time': '18:30',
      'batchNumber': '003',
      'participationPoints': '100 points',
      'state': ParticipationCardState.claimPrize,
    },
    {
      'productName':
          'Mouse Razer Pulsar X120 With Ergonomic Functional (PAW3360 Sensor)',
      'imageUrl':
          'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
      'date': '2025-06-30',
      'time': '18:30',
      'batchNumber': '003',
      'participationPoints': '100 points',
      'state': ParticipationCardState.waiting,
    },
    {
      'productName':
          'Mouse Razer Pulsar X120 With Ergonomic Functional (PAW3360 Sensor)',
      'imageUrl':
          'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
      'date': '2025-06-30',
      'time': '18:30',
      'batchNumber': '003',
      'participationPoints': '100 points',
      'state': ParticipationCardState.alreadyClaimed,
    },
    {
      'productName':
          'Mouse Razer Pulsar X120 With Ergonomic Functional (PAW3360 Sensor)',
      'imageUrl':
          'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
      'date': '2025-06-30',
      'time': '18:30',
      'batchNumber': '003',
      'participationPoints': '100 points',
      'state': ParticipationCardState.notWin,
    },
    {
      'productName':
          'Mouse Razer Pulsar X120 With Ergonomic Functional (PAW3360 Sensor)',
      'imageUrl':
          'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
      'date': '2025-06-30',
      'time': '18:30',
      'batchNumber': '003',
      'participationPoints': '100 points',
      'state': ParticipationCardState.notWin,
    },
    {
      'productName':
          'Mouse Razer Pulsar X120 With Ergonomic Functional (PAW3360 Sensor)',
      'imageUrl':
          'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
      'date': '2025-06-30',
      'time': '18:30',
      'batchNumber': '003',
      'participationPoints': '100 points',
      'state': ParticipationCardState.notWin,
    },

    {
      'productName': 'TNG Voucher RM110',
      'imageUrl':
          'https://images.getbats.com/item/ecget/20230613_9e4c1c2b6aff4a43f3c00c8619e7c6ef.png',
      'date': '2025-06-30',
      'time': '18:30',
      'batchNumber': '003',
      'participationPoints': '100 points',
      'state': ParticipationCardState.claimPrize,
      'prizeType': PrizeType.tngVoucher,
    },
  ];

  // Filter participation data based on selected category
  List<Map<String, dynamic>> get filteredParticipationData {
    if (selectedCategory == 'My Prize') {
      return participationData.where((participation) {
        final state = participation['state'] as ParticipationCardState;
        return state == ParticipationCardState.claimPrize ||
            state == ParticipationCardState.alreadyClaimed;
      }).toList();
    }
    // For 'My Participation' or any other category, return all data
    return participationData;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'Participation Records'),
      body: Column(
        children: [
          // Category Filter
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            child: ParticipationCategory(
              onCategorySelected: (category) {
                setState(() {
                  selectedCategory = category;
                });
              },
              initialCategory: selectedCategory,
            ),
          ),

          // Participation Cards List
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.only(top: 8, bottom: 16),
              itemCount: filteredParticipationData.length,
              itemBuilder: (context, index) {
                final participation = filteredParticipationData[index];
                return ParticipationCard(
                  productName: participation['productName'],
                  imageUrl: participation['imageUrl'],
                  date: participation['date'],
                  time: participation['time'],
                  batchNumber: participation['batchNumber'],
                  participationPoints: participation['participationPoints'],
                  state: participation['state'],
                  prizeType: participation['prizeType'] ?? PrizeType.normal,
                  onClaimPrize: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Claiming prize...'),
                        backgroundColor: Color(0xFFFFBF00),
                      ),
                    );
                  },
                  onClaimTngVoucher:
                      participation['prizeType'] == PrizeType.tngVoucher
                      ? () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const ClaimVoucherView(),
                            ),
                          );
                        }
                      : null,
                  onViewClaimed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Viewing claimed prize details...'),
                        backgroundColor: Color(0xFF0EA5E9),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
