import 'package:flutter/material.dart';
import '../view/ParticipationRecordsView.dart';

class LosePopup extends StatelessWidget {
  final VoidCallback? onClose;
  final VoidCallback? onParticipantRecord;
  final double? imageHeight;
  final double? imageWidth;
  final BoxFit imageFit;

  const LosePopup({
    Key? key,
    this.onClose,
    this.onParticipantRecord,
    this.imageHeight,
    this.imageWidth,
    this.imageFit = BoxFit.cover,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black54,
      child: Center(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Top section with red background
              Container(
                width: double.infinity,
                decoration: const BoxDecoration(
                  color: Color(0xFFFF8FA3), // Red background
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                padding: const EdgeInsets.all(24),
                child: Center(
                  child: Container(
                    width: imageWidth ?? 150, // Fixed width
                    height: imageHeight ?? 150, // Fixed height
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.asset(
                        'assets/images/lose_popup.png', // Fixed asset image
                        fit: imageFit,
                      ),
                    ),
                  ),
                ),
              ),
              // Bottom section with white background
              Container(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Lose text
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: const [
                        SizedBox(width: 8),
                        Text(
                          '😔 Didn’t Win,',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ],
                    ),
                    const Text(
                      'Better Luck Next Time!',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Thank you for joining!',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.black,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Participant Record button
                    SizedBox(
                      width: double.infinity,
                      height: 48,
                      child: ElevatedButton(
                        onPressed:
                            onParticipantRecord ??
                            () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const ParticipationRecordsView(),
                                ),
                              );
                            },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(
                            0xFFFFBF00,
                          ), // Yellow background
                          foregroundColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          'Participant Record',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    // Close button
                    GestureDetector(
                      onTap: onClose ?? () => Navigator.of(context).pop(),
                      child: const Text(
                        'Close',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class LosePopupOverlay {
  static void show({
    required BuildContext context,
    VoidCallback? onParticipantRecord,
    VoidCallback? onClose,
    String? prizeTitle,
    String? brandName,
    String? resellerName,
    String? productImageUrl,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return LosePopup(
          onParticipantRecord: onParticipantRecord,
          onClose: onClose ?? () => Navigator.of(context).pop(),
          imageHeight: 150,
          imageWidth: 150,
          imageFit: BoxFit.cover,
        );
      },
    );
  }
}
