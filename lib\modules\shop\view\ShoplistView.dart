import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view-model/ShoplistViewModel.dart';
import '../widget/ShopCard.dart';
import '../widget/ShopCardSkeleton.dart';
import '../widget/ShopCategoryBar.dart';
import '../widget/ShopCategoryBarSkeleton.dart';
import 'ShopCartView.dart';
import '../../favourite/view/FavouriteView.dart';
import '../widget/PriceRangeFilter.dart';

class ShoplistView extends StatelessWidget {
  const ShoplistView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => ShoplistViewModel(),
      child: const _ShoplistViewBody(),
    );
  }
}

class _ShoplistViewBody extends StatelessWidget {
  const _ShoplistViewBody({Key? key}) : super(key: key);

  IconData _getIconFromString(String iconName) {
    switch (iconName) {
      case 'grid_view':
        return Icons.grid_view;
      case 'lightbulb_outline':
        return Icons.lightbulb_outline;
      case 'devices':
        return Icons.devices;
      case 'headphones':
        return Icons.headphones;
      case 'checkroom':
        return Icons.checkroom;
      case 'face':
        return Icons.face;
      default:
        return Icons.category;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ShoplistViewModel>(
      builder: (context, vm, child) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: CustomScrollView(
            slivers: [
              // Search bar, title, and price range (NOT sticky)
              SliverToBoxAdapter(
                child: Container(
                  color: const Color(0xFFFFE066),
                  padding: const EdgeInsets.fromLTRB(20, 12, 20, 0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Search bar and icons
                      Row(
                        children: [
                          Expanded(
                            child: Container(
                              height: 36,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  const Padding(
                                    padding: EdgeInsets.symmetric(horizontal: 12),
                                    child: Icon(Icons.search, color: Colors.grey, size: 20),
                                  ),
                                  Expanded(
                                    child: TextField(
                                      controller: vm.searchController,
                                      decoration: const InputDecoration(
                                        hintText: '',
                                        border: InputBorder.none,
                                        isDense: true,
                                        contentPadding: EdgeInsets.symmetric(vertical: 8),
                                      ),
                                      onSubmitted: (_) => vm.onSearch(),
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: vm.onSearch,
                                    child: Container(
                                      height: 32,
                                      margin: const EdgeInsets.symmetric(vertical: 2, horizontal: 4),
                                      padding: const EdgeInsets.symmetric(horizontal: 18),
                                      decoration: BoxDecoration(
                                        color: Colors.red[700],
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: const Center(
                                        child: Text(
                                          'Search',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          GestureDetector(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(builder: (_) => const FavouriteView()),
                              );
                            },
                            child: const Icon(Icons.favorite, color: Colors.black, size: 28),
                          ),
                          const SizedBox(width: 8),
                          GestureDetector(
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(builder: (_) => const ShopCartView()),
                              );
                            },
                            child: const Icon(Icons.shopping_cart, color: Colors.black, size: 28),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      // Title and price range
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'Lucky Mall',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.amber[600],
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: GestureDetector(
                              onTap: () {
                                showModalBottomSheet(
                                  context: context,
                                  isScrollControlled: true,
                                  backgroundColor: Colors.transparent,
                                  builder: (_) => const PriceRangeFilter(),
                                );
                              },
                              child: const Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(Icons.tune, size: 16, color: Colors.black),
                                  SizedBox(width: 4),
                                  Text(
                                    'Price Range',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.black,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              // Sticky filter bar (category buttons)
              SliverPersistentHeader(
                pinned: true,
                delegate: _FilterBarDelegate(
                  child: vm.isLoading
                      ? const ShopCategoryBarSkeleton()
                      : ShopCategoryBar(
                          categories: vm.categories,
                          onCategoryTap: vm.onCategoryTap,
                          getIconFromString: _getIconFromString,
                        ),
                ),
              ),
              // Product grid
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverToBoxAdapter(
                  child: Container(
                    color: Colors.white,
                    child: vm.isLoading
                        ? GridView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                              maxCrossAxisExtent: 250,
                              mainAxisSpacing: 12,
                              crossAxisSpacing: 12,
                              childAspectRatio: 0.7,
                            ),
                            itemCount: 16,
                            itemBuilder: (context, index) => const ShopCardSkeleton(),
                          )
                        : vm.products.isEmpty
                            ? const Center(
                                child: Text(
                                  'No products found',
                                  style: TextStyle(fontSize: 16, color: Colors.grey),
                                ),
                              )
                            : GridView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                                  maxCrossAxisExtent: 250,
                                  mainAxisSpacing: 12,
                                  crossAxisSpacing: 12,
                                  childAspectRatio: 0.7,
                                ),
                                itemCount: vm.products.length,
                                itemBuilder: (context, index) {
                                  final product = vm.products[index];
                                  return ShopCard(
                                    product: product,
                                    onTap: () => vm.onProductTap(context, product),
                                    onFavorite: () => vm.onFavoriteTap(context, product),
                                  );
                                },
                              ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// Sticky header delegate
class _FilterBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  _FilterBarDelegate({required this.child});

  @override
  double get minExtent => 110;
  @override
  double get maxExtent => 110;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) => true;
}