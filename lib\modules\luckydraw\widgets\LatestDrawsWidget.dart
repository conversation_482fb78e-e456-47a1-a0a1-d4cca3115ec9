import 'package:flutter/material.dart';
import 'package:luckymall/modules/luckydraw/view/LatestDrawRecordView.dart';

class LatestDrawsWidget extends StatelessWidget {
  const LatestDrawsWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Sample latest draws data
    final List<Map<String, dynamic>> latestDraws = [
      {
        'itemName': 'Vacuum Cleaner',
        'imageUrl':
            'https://www.flashgadgets.com.my/media/catalog/product/cache/75702c449a202b44dcc72e0c4695f594/_/_/__8886419334033.jpg',
        'winningId': '10239112',
        'dateTime': '2025-06-30 18:54',
      },
      {
        'itemName': 'Wok Pan',
        'imageUrl':
            'https://www.lecreuset.com.my/on/demandware.static/-/Sites-lcapac-master-catalog/default/dwc68ddecd/common/catalog/product/5/1/51319320010598-01.jpg',
        'winningId': '10239112',
        'dateTime': '2025-06-30 18:54',
      },
      {
        'itemName': 'Gaming Mouse',
        'imageUrl':
            'https://store.storeimages.cdn-apple.com/1/as-images.apple.com/is/airtag-4pack-select-202104_FV1?wid=890&hei=740&fmt=jpeg&qlt=90&.v=QVI2eUgvdU1qT1VRdEZUOXVUVHgrYmZsbU9lbE5hT3NiWXl3b0NNUWN4YTd3TUxoc0RtTUVrN0JUWDZVL296RXZvdUZlR0V0VUdJSjBWaDVNVG95YkNDQXVVSzBLTjg2d0RLaE1iOTA5c2c',
        'winningId': '10239112',
        'dateTime': '2025-06-30 18:54',
      },
    ];

    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFFFBF00),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 0),
            child: Row(
              children: [
                const Text(
                  'Latest Draws',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const LatestDrawRecordView(),
                      ),
                    );
                  },
                  child: const Text(
                    'See more',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.black87,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Latest draws cards
          Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            decoration: BoxDecoration(
              color: Color(0xFFF9F6EE),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Container(
              height: 200,
              padding: const EdgeInsets.all(16),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: latestDraws.length,
                itemBuilder: (context, index) {
                  final draw = latestDraws[index];
                  return Container(
                    width: 160,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.grey.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Item image
                        Expanded(
                          flex: 3,
                          child: Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(12),
                              ),
                              image: DecorationImage(
                                image: NetworkImage(draw['imageUrl']),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        ),

                        // Item details
                        Expanded(
                          flex: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(12),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Winning ID: ${draw['winningId']}',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black87,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Date time: ${draw['dateTime']}',
                                  style: const TextStyle(
                                    fontSize: 10,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
