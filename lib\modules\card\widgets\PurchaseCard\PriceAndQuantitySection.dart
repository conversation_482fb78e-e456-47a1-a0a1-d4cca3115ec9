import 'package:flutter/material.dart';

class PriceAndQuantitySection extends StatelessWidget {
  final int quantity;
  final double unitPrice;
  final void Function(int) onQuantityChanged;

  const PriceAndQuantitySection({
    super.key,
    required this.quantity,
    required this.unitPrice,
    required this.onQuantityChanged,
  });

  void _handleDecrease(BuildContext context) {
    if (quantity <= 5) {
      // showDialog(
      //   context: context,
      //   builder: (_) => AlertDialog(
      //     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      //     title: const Row(
      //       children: [
      //         Icon(Icons.warning_amber_rounded, color: Colors.orange),
      //         SizedBox(width: 8),
      //         Text("Minimum Reached"),
      //       ],
      //     ),
      //     content: const Text("Minimum purchase is 5 items.",
      //     style: TextStyle(fontSize: 14),),
      //     actions: [
      //       TextButton(
      //         style: TextButton.styleFrom(
      //           foregroundColor: Colors.white,
      //           backgroundColor: Colors.red,
      //           padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      //           shape: RoundedRectangleBorder(borderRadius:  BorderRadius.circular(10)),
      //         ),
      //         onPressed: () => Navigator.pop(context),
      //         child: const Text("OK"),
      //       )
      //     ],
      //   ),
      // );
      showDialog(
    context: context,
    barrierDismissible: true,
    builder: (_) {
      return Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color.fromARGB(255, 248, 234, 189), Color.fromRGBO(255, 255, 255, 1)],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: const [
              BoxShadow(
                color: Colors.white,
                blurRadius: 30,
                spreadRadius: 5,
                offset: Offset(0, 10),
              ),
              BoxShadow(
                color: Color.fromRGBO(255, 191, 0, 1),
                blurRadius: 12,
                spreadRadius: -6,
                offset: Offset(0, 0),
              ),
            ],
            border: Border.all(color: const Color.fromRGBO(255, 191, 0, 1), width: 1),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.info_outline_rounded,
                  color: Color.fromRGBO(255, 12, 69, 1), size: 48),
              const SizedBox(height: 16),
              const Text(
                "Minimum Purchase Required",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 10),
              const Text(
                "You must purchase at least 5 items to continue.",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 14),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: () => Navigator.pop(context),
                  child: const Text(
                    "Okay",
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    },
  );
    } else {
      onQuantityChanged(quantity - 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    final TextEditingController controller =
        TextEditingController(text: quantity.toString());

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(color: Colors.black12, blurRadius: 8, offset: Offset(0, 4)),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                "RM ${unitPrice.toStringAsFixed(2)}",
                style: const TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
              const Spacer(),
              SizedBox(
                height: 40,
                child: Row(
                  children: [
                    _QuantityButton(
                      icon: Icons.remove,
                      onTap: () => _handleDecrease(context),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(6),
                        bottomLeft: Radius.circular(6),
                      ),
                      isDisabledVisual: quantity <= 5,
                    ),
                    SizedBox(
                      width: 50,
                      height: 40,
                      child: TextFormField(
                        controller: controller,
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                        decoration: const InputDecoration(
                          contentPadding: EdgeInsets.symmetric(vertical: 10),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.zero,
                          ),
                        ),
                        onFieldSubmitted: (val) {
                          final int? newVal = int.tryParse(val);
                          if (newVal != null) {
                            if (newVal < 5) {
                              _handleDecrease(context);
                            } else {
                              onQuantityChanged(newVal);
                            }
                          }
                        },
                      ),
                    ),
                    _QuantityButton(
                      icon: Icons.add,
                      onTap: () => onQuantityChanged(quantity + 1),
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(6),
                        bottomRight: Radius.circular(6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            "Minimum Purchase: 5 Items",
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}

class _QuantityButton extends StatefulWidget {
  final IconData icon;
  final VoidCallback onTap;
  final BorderRadius borderRadius;
  final bool isDisabledVisual;

  const _QuantityButton({
    required this.icon,
    required this.onTap,
    required this.borderRadius,
    this.isDisabledVisual = false,
  });

  @override
  State<_QuantityButton> createState() => _QuantityButtonState();
}

class _QuantityButtonState extends State<_QuantityButton> {
  bool _hovering = false;

  @override
  Widget build(BuildContext context) {
    final Color baseColor = _hovering
        ? Colors.amber
        : (widget.isDisabledVisual ? Colors.grey.shade300 : Colors.grey.shade100);

    return MouseRegion(
      onEnter: (_) => setState(() => _hovering = true),
      onExit: (_) => setState(() => _hovering = false),
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: widget.onTap, // Always call tap — dialog handles logic
        child: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: baseColor,
            border: Border.all(color: Colors.grey.shade400),
            borderRadius: widget.borderRadius,
          ),
          child: Icon(
            widget.icon,
            size: 20,
            color: widget.isDisabledVisual ? Colors.grey : Colors.black,
          ),
        ),
      ),
    );
  }
}
